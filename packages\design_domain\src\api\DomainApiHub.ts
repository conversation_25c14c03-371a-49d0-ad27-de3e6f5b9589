import { Vector2Like } from "three";



import { IEntityBase } from "../entity/EntityBase";
import { EntityFurniture } from "../entity/EntityFurniture";
import { EntityType } from "../entity/EntityType";
import { EntityWall } from "../entity/EntityWall";
import { EntityWindow } from "../entity/EntityWindow";
import { EntityDoor } from "../entity/EntityDoor";
import { ServiceManager } from "../service/ServiceManager";
import { LayoutSchemeData } from "./ILayoutScheme";
import { ReqResMsg } from "./ReqResMsg";
import { IPose } from "./IPose";
import { IStoreSeries, StoreSeries } from "../store/StoreSeries";
import { IEntityRoom } from "../entity/EntityRoom";


/**
* @description API 中心，作为外部请求的入口
* 所有对外服务的中介者；
* 对外服务的代理对象；
* <AUTHOR>
* @date 2025-06-16
* @lastEditTime 2025-06-16 12:03:32
* @lastEditors xuld
*/
export class DomainApiHub {
    private static _instance: DomainApiHub;
    private _isInit: boolean = false;

    public static get instance(): DomainApiHub {
        if (!this._instance) {
            this._instance = new DomainApiHub();
        }
        return this._instance;
    }

    /**
     * @description 初始化
     */
    public init(): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        ServiceManager.instance.init();
    }

    /**
     * @description 获取所有实体
     * @returns 所有实体
     */
    public getAllEntities(): IEntityBase[] {
        return ServiceManager.instance.storeService.getAllEntities();
    }

    /**
     * @description 通过 uuid 获取实体
     * @param uuid 实体 uuid
     * @returns 实体对象,如果不存在返回undefined
     */
    public getEntity(uuid: string): IEntityBase | undefined {
        return ServiceManager.instance.storeService.getEntity(uuid);
    }

    /**
    * @description 获取指定类型的实体
    * @param type 实体类型
    * @returns 实体对象数组
    */
    public getEntitiesByType(type: EntityType): IEntityBase[] {
        return ServiceManager.instance.storeService.getEntitiesByType(type);
    }


    /**
     * @description 从布局方案导入
     * @param schemeData 布局方案数据
     */
    public async importFromSchemeData(schemeData: LayoutSchemeData): Promise<ReqResMsg> {
        return ServiceManager.instance.schemeSerializeService.deserializeSchemeData(schemeData);
    }

    /**
     * @description 获取布局方案
     */
    public getScheme(): any {
        return ServiceManager.instance.storeService.getScheme();
    }

    /**
     * @description 创建墙
     * @param start 起点
     * @param end 终点
     * @param thickness 厚度
     * @param height 高度
     * @returns 墙的 UUID
     */
    public async createWall(start: Vector2Like, end: Vector2Like, thickness: number, height: number): Promise<string> {
        let args = {
            type: EntityType.wall,
            startX: start.x,
            startY: start.y,
            endX: end.x,
            endY: end.y,
            thickness: thickness,
            height: height
        };
        const wall = EntityWall.create(args);
        await ServiceManager.instance.storeService.addEntity(wall);
        return wall.uuid;
    }

    /**
     * @description 创建家具图元
     * @param figureElement 图元数据
     * @returns 图元的 UUID
     */
    public async createFurniture(
        category: string,
        subCategory: string,
        shape: string,
        realType: string,
        materialId: string,
        pose: IPose): Promise<string> {
        let args = {
            type: EntityType.furniture,
            category: category,
            materialId: materialId,
            x: pose.x,
            y: pose.y,
            z: pose.z || 0,
            length: pose.length,
            width: pose.width,
            height: pose.height || 0,
            normalX: pose.normalX,
            normalY: pose.normalY,
            normalZ: pose.normalZ,
            subCategory: subCategory,
            mirror: 0,
            shape: shape,
            realType: realType,
        };
        const furniture = EntityFurniture.create(args);
        await ServiceManager.instance.storeService.addEntity(furniture);
        return furniture.uuid;
    }

    /**
     * @description 创建窗户图元
     * @param figureElement 图元数据
     * @returns 图元的 UUID
     */
    public async createWindow(
        eType: string,
        realType: string,
        pose: IPose
    ): Promise<string> {
        let args = {
            type: EntityType.window,
            eType: eType,
            realType: realType,
            mirror: 0,
            x: pose.x,
            y: pose.y,
            length: pose.length,
            width: pose.width,
            normalX: pose.normalX,
            normalY: pose.normalY,
            normalZ: pose.normalZ
        };
        const window = EntityWindow.create(args);
        await ServiceManager.instance.storeService.addEntity(window);
        return window.uuid;
    }

    /**
     * @description 创建门图元
     * @param figureElement 图元数据
     * @returns 图元的 UUID
     */
    public async createDoor(
        eType: string,
        realType: string,
        pose: IPose
    ): Promise<string> {
        let args = {
            type: EntityType.door,
            eType: eType,
            realType: realType,
            mirror: 0,
            x: pose.x,
            y: pose.y,
            length: pose.length,
            width: pose.width,
            normalX: pose.normalX,
            normalY: pose.normalY,
            normalZ: pose.normalZ
        };
        const door = EntityDoor.create(args);
        await ServiceManager.instance.storeService.addEntity(door);
        return door.uuid;
    }

    public async applySeries(series: IStoreSeries) {
        if (!series) {
            console.warn("DomainApiHub.applySeries: 套系数据为空");
            return;
        }
        let rooms = ServiceManager.instance.storeService.getEntitiesByType(EntityType.room) as IEntityRoom[];

        let promises = [];
        for (const room of rooms) {
            const roomSeries = StoreSeries.create({
                kgId: series.kgId,
                ruleId: series.ruleId,
                seriesStyle: series.seriesStyle,
                seriesKgId: series.seriesKgId,
                ruleName: series.ruleName,
                status: series.status,
                thumbnail: series.thumbnail,
            });
            promises.push(ServiceManager.instance.storeService.setRoomSeries(room.uuid, roomSeries));
        }
        if (promises.length > 0) {
            await Promise.all(promises);
        }
        await ServiceManager.instance.materialMatchingService.matchRooms(rooms);
    }
}