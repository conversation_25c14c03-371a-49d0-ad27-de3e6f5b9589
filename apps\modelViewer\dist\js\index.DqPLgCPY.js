const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./bootstrap.Bq6d6zZl.js","../css/bootstrap.cKP7Haty.css"])))=>i.map(i=>d[i]);
(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))l(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const n of t.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&l(n)}).observe(document,{childList:!0,subtree:!0});function c(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function l(e){if(e.ep)return;e.ep=!0;const t=c(e);fetch(e.href,t)}})();const y="modulepreload",g=function(d,i){return new URL(d,i).href},h={},v=function(i,c,l){let e=Promise.resolve();if(c&&c.length>0){const n=document.getElementsByTagName("link"),r=document.querySelector("meta[property=csp-nonce]"),m=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));e=Promise.allSettled(c.map(o=>{if(o=g(o,l),o in h)return;h[o]=!0;const u=o.endsWith(".css"),p=u?'[rel="stylesheet"]':"";if(!!l)for(let a=n.length-1;a>=0;a--){const f=n[a];if(f.href===o&&(!u||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${p}`))return;const s=document.createElement("link");if(s.rel=u?"stylesheet":y,u||(s.as="script"),s.crossOrigin="",s.href=o,m&&s.setAttribute("nonce",m),document.head.appendChild(s),u)return new Promise((a,f)=>{s.addEventListener("load",a),s.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${o}`)))})}))}function t(n){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=n,window.dispatchEvent(r),!r.defaultPrevented)throw n}return e.then(n=>{for(const r of n||[])r.status==="rejected"&&t(r.reason);return i().catch(t)})};v(()=>import("./bootstrap.Bq6d6zZl.js"),__vite__mapDeps([0,1]),import.meta.url);
