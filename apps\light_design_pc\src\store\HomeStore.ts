import { makeAutoObservable } from "mobx";

import { HomeState } from "./HomeState";
import { Object2DBase } from "@layoutai/design_2d";

/**
 * @description 主页store
 * <AUTHOR>
 * @date 2025-06-19
 * @lastEditTime 2025-06-19 11:14:12
 * @lastEditors xuld
 */

export default class HomeStore {
    currentState: HomeState = HomeState.State2D; // 主页状态
    currenScheme = {} as any; // 当前方案
    roomInfos = [] as any; // 当前方案所有房间信息
    designMode = "AiCadMode" as string; // 当前模式（用于控制左侧面板内容）
    selectObject = {} as Object2DBase; // 当前选中的对象（画布）

    constructor() {
        makeAutoObservable(this, {}, { autoBind: true });
    }

    setState(data: HomeState) {
        this.currentState = data;
    }
    setCurrenScheme(data: any) {
        this.currenScheme = data;
    }
    setRoomInfos(data: any) {
        this.roomInfos = data;
    }
    setDesignMode(data: string) {
        this.designMode = data;
    }
    setSelectObject(data: Object2DBase) {
        this.selectObject = data;
    }
}
