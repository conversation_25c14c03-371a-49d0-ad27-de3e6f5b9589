


import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react';
import { Model3dViewer, Model3dApi, Model3dSimplifier,DesignMaterialService, Model3dApiConfigs, Cabinet3DApi, SimpleGlbParser, SvgGltfCabinetNode, GltfManager, EdgesBuilder, SvgGltfFurnitureNode, SvgGltfBoardBatchNode, SvgGltfComponentNode, LayoutCustomCabinetInfoService, I_CustomCabinetInfoItem, SvgGltfNode, SvgGltfSwingDoorLeafNode } from '@layoutai/model3d_api';
import { Button, Segmented } from 'antd';
import "./index.less"
import { Box3, BufferGeometry, DoubleSide, Float32BufferAttribute, Group, Intersection, Mesh, MeshStandardMaterial, Object3D, Vector3 } from 'three';
import { ObjectLoader } from 'three';
import Object3dTreeItem from './Object3dTreeItem';
import { MeshListItem } from './MeshListItem';
import { generateUUID } from 'three/src/math/MathUtils.js';
import { GLTFLoader } from 'three/examples/jsm/Addons.js';
import SeriesMatcherPanel from './SeriesMatcherPanel';
import { Sleep, compareNames } from "@layoutai/z_polygon";
import { GltfNodeTreeItem } from './GlbJsonViewer';
import StyleBrushPanel from './StyleBrushPanel';
import { StyleBrush } from '@layoutai/basic_data';
import { OutlinePostProcess } from '@layoutai/effects3d';
import { RoySceneExporter } from '@layoutai/royscene_exporter';
import ReplaceMaterialPanel from './ReplaceMaterialPanel';


const ViewerProps = {
  updateCount : 1,
  currentModel : null
}

/**
 * @description 套系查看器
 * 输入套系id，查看套系3D样式、节点树和网格列表
 */
const SeriesModelViewerPage: React.FC = () => {
  const mainDivRef = useRef(null);
  const [currentModel,setCurrentModel]= useState<Object3D>(null);
  const [isMergedNodes,setIsMergeNodes] = useState<boolean>(true);
  const [currentInfo,setCurrentInfo] = useState<{url:string,materialId:string,isGlbJson?:boolean,styleBrushId?:string}>(null);
  const [glbJsonUrlList,setGlbJsonUrlList] = useState<I_CustomCabinetInfoItem[]>([]);
  const [currentLeftTabName,setCurrentLeftTab] = useState<string>("Series");
  const [currentRightTabName,setCurrentRightTab]  = useState<string>("details");
  const [updateCount,setUpdateCount] = useState<number>(ViewerProps.updateCount);
  const leftPanelModeOptions = [
    {
      label: "套系",
      value: "Series"
    },
    {
      label: "风格刷",
      value: "StyleBrush"
    },
    {
      label : "替换",
      value : "Replace"
    }
  ];
  const rightPanelModeOptions = [
    {
      label:"模型",
      value:"details"
    },{
      label:"JSON列表",
      value:"jsonList"
    }
  ];

  const updateMeshCount = (object: Object3D) => {
    let counter = 0;
    if ((object as Mesh).isMesh) {
      counter += 1;
      let mesh = object as Mesh;
      if (mesh.geometry) {
        // mesh.visible = (mesh.geometry?.attributes?.position?.count || 0) == 16
      }
      if (mesh.material) {
        let material = mesh.material as MeshStandardMaterial;
        if (material instanceof Array) {
          material.forEach((ele) => {
            if (ele.side) ele.side = DoubleSide;
          })
        }
        else {
          if (material.side) {
            material.side = DoubleSide;
          }
        }

      }
    }
    if (object.children && object.children.length > 0) {
      object.children.forEach((child, index) => {
        counter += updateMeshCount(child);
      });
    }
    object.userData.meshesCount = counter;
    return counter;
  }

  const updateEdges = (group:Object3D)=>{
    if(group)
    {
      EdgesBuilder.makeEdgesOfObject(group,20,null,{ignoreNames:["SvgGltfFurnitureNode"]});
    }
  }
  const onloadModel = async (group_node: Object3D) => {

    updateMeshCount(group_node);

    Model3dViewer.instance.setMainGroup(group_node as Group);

    updateEdges(group_node);

    if(group_node)
    {
        EdgesBuilder.makeEdgesOfObject(group_node,20,null,{ignoreNames:["SvgGltfFurnitureNode"]});
    }
    setCurrentModel(group_node);

    ViewerProps.currentModel = group_node;
  }


  const loadGlbJsonUrl = async (materialId: string,url: string) => {
    setCurrentInfo({ url: url, materialId: materialId,isGlbJson:true});


    let last_id = url.lastIndexOf("_");
    let jsonDataUrl = url.substring(0, last_id) + "_0.json"+"?t="+Math.floor(Math.random()*100000);
    let data = await fetch(jsonDataUrl).then(val => val.json());

    let svgGltfCabinetNode = new SvgGltfCabinetNode(data);
    if (isMergedNodes) {
      svgGltfCabinetNode.mergeBatchedNodes();
    }

    svgGltfCabinetNode.updateCabinetsBox();

    await svgGltfCabinetNode.updateSolidModels();
    onloadModel(svgGltfCabinetNode);

    if(!glbJsonUrlList[0] || glbJsonUrlList[0]?.materialId !== materialId)
    {
        let res = await LayoutCustomCabinetInfoService.listByPage({materialId:materialId,styleId:"0", updateDateStart:"2025-06-16 00:05:12"});
        if(res)
        {
            res.sort((a,b)=>{
              return -a.updateDate.localeCompare(b.updateDate);
            })
        }
        setGlbJsonUrlList(res);
    }
    return true;

  }

  const loadModelId = async (materialId: string) => {
    let design_material_info = (await DesignMaterialService.getDesignMaterialInfoByIds([materialId]))[0] || null;
    if (!design_material_info) return;
    setCurrentInfo({materialId:materialId,url:design_material_info.A3dSource,isGlbJson:false});
    if (design_material_info.A3dSource) {
      let model = await Model3dApi.MakeMesh3D_WithA3dSource(materialId, design_material_info.A3dSource, true, false);
      if (!model) {
        model = await Model3dApi.MakeMesh3D_WithA3dSource(materialId, design_material_info.A3dSource, false, false);
      }
    }
  }

  const onApplyStyleBrush = (brush:StyleBrush)=>{
    console.log(brush);
    if(currentModel)
    {
        GltfManager.updateCabinet3DModelWithStyleBrush(currentModel as Group,null,{styleBrush:brush}).then(()=>{
          if(currentInfo && currentInfo.isGlbJson)
          {
            setCurrentInfo({materialId:currentInfo.materialId,url:currentInfo.url, isGlbJson:true, styleBrushId:brush.id});
          }

          updateMeshCount(currentModel);
          setUpdateCount(updateCount+1);
          updateEdges(currentModel);

          setCurrentModel(null);
          Sleep(500).then(()=>{
            setCurrentModel(currentModel);
          })
        })
    }
  }

  const onExportRoyScene = async ()=>{
    let roySceneExporter = new RoySceneExporter();
    let schemeId = "modelviewer"+"2025";
    if(currentInfo)
    {
        schemeId = "modelviewer";
        schemeId += currentInfo.materialId
        if(currentInfo.styleBrushId)
        {
          schemeId += "style"+currentInfo.styleBrushId;
        }
        else{
          schemeId += "default";
        }
    }
    Model3dViewer.instance.updateSelectionTarget(null);
    let rsRes = await roySceneExporter.exportScene(Model3dViewer.instance.scene,schemeId).catch((e)=>{console.log(e)});
    rsRes = RoySceneExporter.exportSceneInfo;
    let url = `https://xr.3vjia.com/appAILightDesign/?id=${rsRes.roySceneId}&roySceneVersion=${rsRes.roySceneSchemeId}&workMode=svgRoyScene`
    console.log(url);
    window.open(url, "_blank");
  }
  const exportModel = async () => {
    if (currentModel) {
      Model3dViewer.instance.downloadModel("test");
    }
  }
  const clearModelDomExpand = ()=>{ 
    if(ViewerProps.currentModel)
    {
      ViewerProps.currentModel.traverse((child)=>{
        if(child.userData.isDomSelected !== undefined)
        {
          delete child.userData.isDomSelected;
        }
        child.userData.isDomExpand = false;
      });
    }
  }

  const updateForce = ()=>{
    ViewerProps.updateCount++;
    // console.log(updateCount);
    setUpdateCount(ViewerProps.updateCount);
  }

  useEffect(() => {
    if (mainDivRef) {
      Model3dViewer.instance.bindParentDiv(mainDivRef.current as HTMLDivElement);

      let model3dviewer = Model3dViewer.instance;
      // let postEffect = new OutlinePostProcess(model3dviewer.renderer, model3dviewer.scene, model3dviewer.camera, model3dviewer.renderer.domElement.width, model3dviewer.renderer.domElement.height);
      // postEffect.outline_style_mode = 1;
      // model3dviewer.postProcessor = postEffect;
      // postEffect.camera = model3dviewer.camera;
      // postEffect.setDefaultEnabled(true);
      // postEffect.setEnabled(true);
      // Model3dViewer.instance.onResize(true);
      Model3dViewer.instance.startRender();


      Model3dViewer.instance.bindSelectedByRayCallback((ints:Intersection[])=>{
          if(ints[0])
          {
              let it0 = ints[0];
              let object = it0.object;
              if(Model3dViewer?.instance?.updateSelectionTarget)
              {
                  clearModelDomExpand();

                  let obj = object;
                  let gltfObject : SvgGltfNode = null;
                  while(obj){
                    if(obj instanceof SvgGltfFurnitureNode || obj instanceof SvgGltfSwingDoorLeafNode
                      || obj instanceof SvgGltfBoardBatchNode)
                    {
                      if(!gltfObject)
                      {
                        gltfObject = obj;
                      }
                    }
                    obj.userData.isDomExpand = true;
                    obj = obj.parent;
                  }
                  let selectd_object = gltfObject||object;
                  selectd_object.userData.isDomSelected = true;
                  Model3dViewer.instance.updateSelectionTarget(selectd_object);

                  updateForce();
              }
          }
      });
    }
  }, []);

  useEffect(() => {
    if (currentInfo && currentInfo.isGlbJson) {
      loadGlbJsonUrl(currentInfo.materialId,currentInfo.url);
    }
  }, [isMergedNodes]);

  return (
    <>
      <div className='leftTabs'>
        <Segmented options={leftPanelModeOptions} defaultValue={currentLeftTabName} onChange={(value) => setCurrentLeftTab(value)}></Segmented>
        <div className='tabItem' style={{ display: currentLeftTabName === "Series" ? "block" : "none" }}>
          <SeriesMatcherPanel onSelectedItem={(item) => {
            if (item?.similarGlbUrl) {
              if(!item.materialId)
              {
                item.materialId = item.modelId;
              }
              // queryCabinetsData(item.materialId);
              loadGlbJsonUrl( item.materialId,item.similarGlbUrl);
            }
            else if (item.modelId) {
   
              loadModelId(item.modelId);
            }
          }}></SeriesMatcherPanel>
        </div>
        <div className='tabItem' style={{display:currentLeftTabName==="StyleBrush"?"block":"none"}}>
            <StyleBrushPanel onSelectedStyle={onApplyStyleBrush}></StyleBrushPanel>
        </div>
        <div className='tabItem' style={{display:currentLeftTabName==="Replace"?"block":"none"}}>
            <ReplaceMaterialPanel node={null} ></ReplaceMaterialPanel>
        </div>  
      </div>

      <div className='SeriesModelViewContainer' style={{ position: "absolute", left: 300 }}>
        <div id="Model3DViewer" className='main_3d_div' ref={mainDivRef}></div>
        <div className='modelTreeContainer'>
          <Segmented options={rightPanelModeOptions} defaultValue={currentRightTabName} onChange={(value) => setCurrentRightTab(value)}></Segmented>


            {currentRightTabName=="details" && <>
              <div style={{position:"absolute", right:0} }>
                  <Button onClick={()=>{
                    onExportRoyScene();
                  }}>生成royScene</Button>
              </div>
              <div className='tabTile' onClick={(ev) => { setIsMergeNodes(!isMergedNodes); }}>
                {isMergedNodes ? "合批" : "不合批"}
              </div>
              {currentModel && <GltfNodeTreeItem object={currentModel as any} prefix={''} level={0} updateCount={updateCount}></GltfNodeTreeItem>}
            </>}
            {
              currentRightTabName=="jsonList" && <>
                  {glbJsonUrlList.map((item,index)=><div key={"item"+index} className='rowDiv' onClick={()=>{
                      if(!item.valueUrl.startsWith("http"))
                      {
                        item.valueUrl = "https://3vj-content.3vjia.com/" +item.valueUrl;
                      }
                      loadGlbJsonUrl( item.materialId,item.valueUrl);
                      
                  }}>
                      <span className='urlSpan'>{item.valueUrl.substring(item.valueUrl.lastIndexOf("/")+1)}{compareNames([item.valueUrl.substring(item.valueUrl.lastIndexOf("/")+1)],[currentInfo.url])?"✔":""}</span>
                      <span className='urlUpdateDate'>{item.updateDate}</span>
                  </div>)}
              </>
            }


        </div>
      </div>
    </>
  );
};


export default observer(SeriesModelViewerPage);
