import React, { useEffect, useState } from "react";
import useStyles from "./style";
import { useTranslation } from "react-i18next";
import { observer } from "mobx-react-lite";
import { DragEventListener } from "@svg/antd-cloud-design";
import { appContext } from "../../../../context/AppContext";
import { Design2DStates } from "../../../../context/fsm/const/FSMConst";
import { listType } from "../constants";

/**
 * @description 缩略图展示列表（用于户型编辑面板中门窗、结构件部分）
 */

interface Module {
    image: string;
    png: string;
    title: string;
    label: string;
    group_code?: string;
    icon: string;
}

interface FigureListProps {
    data: Module[];
    type: string;
}

const FigureList: React.FC<FigureListProps> = ({ data, type }) => {
    const { t } = useTranslation();
    const { styles } = useStyles();
    const scrollContainerRef = React.useRef<HTMLDivElement>(null);

    useEffect(() => {
        const dragEventListener = new DragEventListener({
            // 展示缩略图
            isShowThumbnail: true,
            container: document.getElementById("side_pannel") as HTMLElement,
            // 打印
            log: false,
        });
        dragEventListener.bindDrag();
        return () => {
            dragEventListener.unbindDrag();
        };
    }, []);

    useEffect(() => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = 0; // 将滚动位置设置为 0
        }
    }, [data]);

    return (
        <div className={styles.figure} ref={scrollContainerRef}>
            {data.map((item, index) => (
                <div
                    key={index}
                    className="item"
                    onPointerDown={(event) => {
                        if (type == listType.WinDoor) {
                            appContext.mainFSM.transitionTo(Design2DStates.DRAW_WINDOOR, {event, item});
                        } else if (type == listType.Structure) {
                            console.log("绘制结构件");
                        }
                    }}
                >
                    <div className="image">
                        <svg className="icon" aria-hidden="true" style={{ width: 60 }}>
                            <use xlinkHref={`#${item.icon}`}></use>
                        </svg>
                    </div>
                    <div className="title">{t(item.title)}</div>
                </div>
            ))}
        </div>
    );
};

export default observer(FigureList);
