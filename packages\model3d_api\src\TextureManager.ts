import {
  Mesh,
  MeshStandardMaterial,
  RepeatWrapping,
  SRGBColorSpace,
  Texture,
  TextureLoader
} from 'three';
import { UserDataKey } from './NodeName';
import { MaterialManager } from './MaterialManager';

export interface I_TextureCacheOptions
{
  category?:string,cabinetStyleId?:string,cabinetItemName?:string,
  picWidth?:number,picHeight?:number
}
/**
 *  管理材质贴图
 */
export class TextureManager {
  private static _instance: TextureManager = null;
  public static readonly IsCacheTexture : string = "IsCacheTexture";

  private _texture_cache: {
    [key: string]: { materialId: string; imgUrl: string; texture: Texture, options:I_TextureCacheOptions};
  } = {};

  private _cache_categories = ["Cabinet"];
  private constructor() {
    this._texture_cache = {};
  }
  public static get instance(): TextureManager {
    if (!TextureManager._instance) {
      TextureManager._instance = new TextureManager();
    }
    return TextureManager._instance;
  }

  clearCache(validCabinetStyleIds:string[]=[])
  {
     let removed_material_ids = [];
     for(let material_id in this._texture_cache)
     {
        let data = this._texture_cache[material_id];
        if(!validCabinetStyleIds.includes(data?.options?.cabinetStyleId||"---"))
        {
            removed_material_ids.push(material_id);
        }
     };
     removed_material_ids.forEach((material_id)=>{
      if(this._texture_cache[material_id]){
        let texture_data = this._texture_cache[material_id];
        let texture = texture_data.texture;
        if(texture && texture.userData[TextureManager.IsCacheTexture])
        {
            texture.userData[TextureManager.IsCacheTexture] = null;
            delete texture.userData[TextureManager.IsCacheTexture];
            texture.dispose();
        }
        delete this._texture_cache[material_id];        
      }
     });
  }
  findTextureInCache(material_id:string)
  {
    if(this._texture_cache[material_id])
    {
      return this._texture_cache[material_id];
    }
    return null;
  }
  getTexture(material_id:string, url:string,options:I_TextureCacheOptions,onload?:(t:Texture)=>void)
  {
      let texture : Texture = null;
      if(this._texture_cache[material_id])
      {
          texture = this._texture_cache[material_id].texture;
          if(texture)
          {
            if(onload){
              onload(texture);
            }
            return texture;
          }

          
      }
      if(!texture) {
        texture = new TextureLoader().load(url, tex => {
            texture = tex;
            texture.wrapS = RepeatWrapping;
            texture.wrapT = RepeatWrapping;
            texture.colorSpace = SRGBColorSpace;
            texture.needsUpdate = true;
            texture.flipY = false;

            if(options.category === "Cabinet") // 如果是定制柜的材质
            {
              if(options.picWidth && options.picHeight)
              {
                texture.repeat.set(texture.image.width/options.picWidth,texture.image.height/options.picHeight);

              }
            }
            if(onload){
              onload(texture);
            }
          });
      }
      if(material_id && material_id.length > 2 && this._cache_categories.includes((options.category||""))) // 明确是一个材质Id
      {
        if(!this._texture_cache[material_id])
        {
            this._texture_cache[material_id] = {
                materialId : material_id,
                imgUrl : url,
                texture : texture,
                options : options
            }
        }

        if(this._texture_cache[material_id])
        {
            texture = this._texture_cache[material_id].texture;
            texture.userData[TextureManager.IsCacheTexture] = true;

        }
      }
      return texture;
      
  }
  static updateMeshTexture(mesh: Mesh, img: HTMLImageElement, materialId: string,options:{category?:string,cabinetStyleId?:string}={}) {
    TextureManager.updateMeshTextureWithImg(mesh, img.src, materialId,options);
  }

  

  static async updateMeshTextureWithImg(mesh: Mesh, url: string, materialId: string,options:I_TextureCacheOptions={}) {
    let material = mesh.userData[UserDataKey.key_standard_material];

    if (!material) return;
    if (material.map) {
        // if(mesh.userData[UserDataKey.MaterialId] === materialId) // 如果Id一样， 就不要重复赋值
        // {
        //    return;
        // }

      let old_texture = (material.map as Texture);
      if(!old_texture.userData  || !old_texture.userData[TextureManager.IsCacheTexture])
      {
            old_texture.dispose();
      }
    }
    let texture = TextureManager.instance.getTexture(materialId,url,options); 

    if (material !== MaterialManager.white_standard_material) {
      let std_material = material as MeshStandardMaterial;
      
      (material as MeshStandardMaterial).map = texture;
      if(std_material.emissiveIntensity > 0.5)
      {
        (material as MeshStandardMaterial).emissiveMap = texture;

        (material as MeshStandardMaterial).emissive = (material as MeshStandardMaterial).color.clone();

      }
      
      mesh.userData[UserDataKey.MaterialId] = materialId;

      // 同步更新 uniforms 中 map 的值
      if (material && material.uniforms && material.uniforms.map) {
        (material as any).uniforms.map.value = texture;
      }
    }

    if (mesh.userData[UserDataKey.key_color_material]) {
      if (
        (mesh.userData[UserDataKey.key_color_material] as MeshStandardMaterial).map !== undefined
      ) {
        (mesh.userData[UserDataKey.key_color_material] as MeshStandardMaterial).map = texture;
      }
    }

    (material as MeshStandardMaterial).needsUpdate = true;
  }
}
