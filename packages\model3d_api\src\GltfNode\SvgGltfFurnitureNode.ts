import { Box3, Group, Object3D, Vector3, Vector3Like } from "three";
import { SvgGltfNode } from "./SvgGltfNode";
import { RawGltfNode } from "./RawGltfNode";
import { MeshName, UserDataKey } from "../NodeName";
import { Model3dApi } from "..";
import { I_DesignMaterialInfo } from "@layoutai/basic_data";


/**
 * 成品节点类
 * 用于处理家具和电器等成品模型
 */
export class SvgGltfFurnitureNode extends SvgGltfNode {
    protected _rawGltfNode: RawGltfNode;
    protected _solidNode: Group;
    public readonly isSvgGltfFurnitureNode = true;

    protected _PosAlignment : Vector3Like = {x:0,y:0,z:0};
    protected _SizeAlignment : Vector3Like = {x:0,y:0,z:0};

    protected _originSolidBox : Box3;
    constructor() {
        super();
        this.name = "SvgGltfFurnitureNode";
        this._solidNode = new Group();
        this._solidNode.name = "SvgGltfSolidNode";
        this._PosAlignment = {x:0,y:0,z:0};
        this._SizeAlignment = {x:0,y:0,z:0};
        this.add(this._solidNode);
    }

    /**
     * 获取实体网格
     */
    get solidMesh(): Object3D {
        return this._rawGltfNode.solidMesh;
    }

    /**
     * 设置实体网格
     */
    set solidMesh(obj: Object3D) {
        if (this._rawGltfNode.solidMesh !== obj) {
            this._rawGltfNode.solidMesh = obj;

            let rawNodeSize = this._rawGltfNode.rawNodeSize;
            let box = SvgGltfNode.UpdateBox3(obj);
            let size = box.getSize(new Vector3());
            let center = box.getCenter(new Vector3());
            let scale = new Vector3(1,1,1);
            if(rawNodeSize)
            {
                let length = rawNodeSize.length;
                let height =rawNodeSize.height;
                let width = rawNodeSize.width;
                let scale = new Vector3(1,1,1);
                if(this._SizeAlignment.x == 1 || this._SizeAlignment.x >= 1. - 1e-2)
                {
                    scale.x = length / size.x;
                }
                if(this._SizeAlignment.y >= 1 || this._SizeAlignment.y >= 1. - 1e-2)
                {
                    scale.y = width / size.y;
                }
                if(this._SizeAlignment.z >= 1 || this._SizeAlignment.z >= 1. -1e-2)
                {
                    scale.z = height / size.z;
                }
            }
            let bottom_center = center.clone().setZ(0);
            let offset = this._originSolidBox.getCenter(new Vector3()).clone().setZ(0).sub(bottom_center);
            this._solidNode.position.copy(offset);
            this._solidNode.scale.copy(scale);
            this._solidNode.add(obj);
        }
    }

    /**
     * 从原始节点创建
     * @param rawGltfNode 原始GLTF节点
     * @returns 当前实例
     */
    fromRawNode(rawGltfNode: RawGltfNode): SvgGltfFurnitureNode {
        this._rawGltfNode = rawGltfNode;
        rawGltfNode.svgNode = this;

        this._category = this._rawGltfNode.boardCategory;
        this._materialId = this._rawGltfNode.materialId;
        this._materialMapVoId = this._rawGltfNode.materialMapVoId || null;

        // 应用变换矩阵
        let matrixWorld = this._rawGltfNode.matrixWorld.clone();
        this.position.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.scale.set(1, 1, 1);
        this.updateMatrix();
        this.applyMatrix4(matrixWorld);

   
        // 添加实体网格
        if (this._rawGltfNode) {
            this.add(this._rawGltfNode.solidMesh);

            let bbox = SvgGltfNode.UpdateBox3(this._rawGltfNode.solidMesh);
            this._originSolidBox = bbox.clone();
        }


        // 设置用户数据
        // this.userData[UserDataKey.MaterialId] = this._rawGltfNode.materialId;
        // this.userData[UserDataKey.FurnitureType] = MeshName.Model;
        // this.userData[UserDataKey.IsModel] = true;

        this.category = this._rawGltfNode.boardCategory;
        this.userData.extras = this._rawGltfNode.userData.extras;

        return this;
    }

    /**
     * 加载并更新实体网格
     */
    public async loadAndUpdateSolidMesh(options:{designMaterialInfo?:I_DesignMaterialInfo}={},PosAlignment?:Vector3Like,SizeAlignment?:Vector3Like): Promise<void> {
        if(options.designMaterialInfo && options.designMaterialInfo.MaterialId === this.materialId)
        {   
            this.designMaterialInfo = options.designMaterialInfo;
        }
        if (!this.designMaterialInfo) return;

        if(PosAlignment)
        {
            this._PosAlignment = PosAlignment;
        }
        if(SizeAlignment)
        {
            this._SizeAlignment = SizeAlignment;
        }
        let a3dSource = this.designMaterialInfo.A3dSource;
        if (a3dSource) {
            let group_node = await Model3dApi.MakeMesh3D_WithA3dSource(
                this.materialId,
                a3dSource,
                true,
                false
            );
            
            if (group_node) {
                let box3 = SvgGltfNode.UpdateBox3(group_node);
                let center = box3.getCenter(new Vector3());
                
                this.solidMesh = group_node;
                this.solidMesh.name = "Model" + this._designMaterialInfo.MaterialName;

                this.solidMesh.userData[UserDataKey.MaterialId] = this.designMaterialInfo.MaterialId;
                this.solidMesh.userData[UserDataKey.FurnitureType] = MeshName.Model;
                this.solidMesh.userData[UserDataKey.IsModel] = true;
            }
        }
    }
} 