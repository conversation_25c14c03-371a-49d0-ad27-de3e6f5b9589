import { Box3, BoxGeometry, Float32BufferAttribute, Group, Matrix3, Matrix4, Mesh, MeshStandardMaterial, Object3D, Vector2, Vector3 } from "three";
import { BoardCategory, I_OrientationType, I_RawGltfNode } from "./types";
import { compareNames } from "@layoutai/z_polygon";
import { SvgGltfNode } from ".";

/**
 * 原始GLTF节点类
 * 用于处理从GLTF文件解析出来的原始节点数据
 */
export class RawGltfNode extends Group {
    private _rawData: I_RawGltfNode;
    private _isSolid: boolean;
    private _solidMesh?: Object3D;


    private _orientationType: I_OrientationType = null;
    private _rawParent: RawGltfNode; // 原始的父亲
    private _rawChildren: RawGltfNode[];


    /**
     *  绑定所生成的节点
     */
    private _svgNode: SvgGltfNode = null;

    constructor(data: I_RawGltfNode) {
        super();
        this._rawData = data || { matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1] };
        this.name = this._rawData.name || "";
        
        // 处理扩展数据
        if (this._rawData.extensions) {
            if (!this._rawData.extras) {
                this._rawData.extras = { ...this._rawData.extensions };
            }
        }
        this._rawData.extras = this._rawData.extras || {};
        this.userData.extras = this._rawData.extras;

        // 处理显示名称
        if (this?._rawData?.extras?.displayName) {
            this._rawData.extras.name = this._rawData.extras.displayName;
        }
        
        this._isSolid = false;
        if (this._rawData.mesh !== undefined) {
            this.name += "mesh" + this._rawData.mesh;
        }
        this._rawChildren = [];
    }

    /**
     * 获取材质ID
     */
    get materialId(): string {
        return this.rawData?.extras?.materialId || "";
    }

    /**
     * 获取材质映射ID
     */
    get materialMapVoId(): string {
        return this.rawData?.extras?.materialMapVoId || "";
    }

    /**
     * 获取分类
     */
    public get category(): string {
        return this.boardCategory;
    }

    /**
     * 设置分类
     */
    public set category(value: string) {
        this.boardCategory = value as any;
    }

    /**
     * 获取板件分类
     */
    get boardCategory(): BoardCategory {
        return this.userData.boradCategory || "";
    }

    /**
     * 设置板件分类
     */
    set boardCategory(t: BoardCategory) {
        this.userData.boradCategory = t;
    }

    public get orientationType(): I_OrientationType {
        return this._orientationType;
    }
    public set orientationType(value: I_OrientationType) {
        this._orientationType = value;
    }
    public get rawParent(): RawGltfNode {
        return this._rawParent;
    }
    public set rawParent(value: RawGltfNode) {
        this._rawParent = value;
    }
    public get rawChildren(): RawGltfNode[] {
        return this._rawChildren;
    }
    public set rawChildren(value: RawGltfNode[]) {
        this._rawChildren = value;
    }

    public get svgNode(): SvgGltfNode {
        return this._svgNode;
    }
    public set svgNode(value: SvgGltfNode) {
        this._svgNode = value;
    }

    /**
     * 重新初始化矩阵
     */
    reInitMatrix(): void {
        let matrix = new Matrix4();
        if (this._rawData.matrix) {
            matrix.fromArray(this._rawData.matrix);
        }
        this.position.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.scale.set(1, 1, 1);
        this.applyMatrix4(matrix);
    }
    initRawChildrenFromObjectChildren()
    {
        this.rawChildren.push(...this.children as any);
    }

    /**
     * 获取原始数据
     */
    get rawData(): I_RawGltfNode {
        return this._rawData;
    }

    /**
     * 获取子节点ID列表
     */
    get childrenIds(): number[] {
        return this._rawData.children || [];
    }

    /**
     * 是否为实体
     */
    get isSolid(): boolean {
        return this._isSolid;
    }

    /**
     * 设置是否为实体
     */
    set isSolid(t: boolean) {
        this._isSolid = t;
    }

    /**
     * 获取实体网格
     */
    get solidMesh(): Object3D {
        return this._solidMesh;
    }

    /**
     * 设置实体网格
     */
    set solidMesh(obj: Object3D) {
        if (this._solidMesh) {
            this._solidMesh.removeFromParent();
            this._solidMesh = null;
        }
        this._solidMesh = obj;

        if (this._solidMesh) {
            if (this.rawNodeSize) {
                this.reInitMatrix();
                let length = this.rawNodeSize.length;
                let height = this.rawNodeSize.height;
                let width = this.rawNodeSize.width;
                let bbox =  SvgGltfNode.UpdateBox3(this.solidMesh);
               
                let b_size = bbox.getSize(new Vector3());
                this.scale.set(length / b_size.x, width / b_size.y, height / b_size.z);
            }
        }
        this.add(this._solidMesh);
    }

    /**
     * 获取原始节点尺寸
     */
    get rawNodeSize(): { length: number; width: number; height: number } | null {
        if (!this._rawData.extras?.length) {
            return null;
        }
        let width = this._rawData?.extras?.width || 10;
        let length = this._rawData?.extras?.length || 10;
        let height = this._rawData?.extras?.height || 10;
        return { length: length, width: width, height: height };
    }

    /**
     * 如果是实体则更新盒子网格
     */
    updateBoxMesh_IfSolid(): void {
        if (this.isSolid) {
            let rawNodeSize = this.rawNodeSize;
            let length = 1;
            let width = 1;
            let height = 1;
            if(rawNodeSize)
            {
                 width = rawNodeSize.width;
                 length = rawNodeSize.length;
                 height = rawNodeSize.height;
            }
            this._solidMesh = new Mesh(
                new BoxGeometry(length, width, height),
                new MeshStandardMaterial({
                    color: 0xe5e5e5,
                    emissive: 0xe5e5e5,
                    emissiveIntensity: 1.,
                    roughness: 1.,
                    metalness: 1.,
                    transparent: false
                })
            );
            let geometry = (this._solidMesh as Mesh).geometry;
            let uvs = geometry.attributes.uv as Float32BufferAttribute;
            if(uvs)
            {
                for(let i=0; i < uvs.count; i++)
                {
                    let x = uvs.getX(i);
                    let y = uvs.getY(i);

                    let v = new Vector2(x,y);

                    let mat3 = new Matrix3();
                    // console.log(length,width,height,this.rawData.extras.displayName);

                    if(compareNames([this.rawData?.extras?.displayName],["踢脚","平板基础板","挡板"]))
                    {
                        mat3.multiply(new Matrix3().makeScale(-length/1000.,-width/1000.));
                        // mat3.multiply(new Matrix3().makeRotation(Math.PI/2));
                    }
                    else{
                        mat3.multiply(new Matrix3().makeRotation(Math.PI/2));
                        mat3.multiply(new Matrix3().makeScale(-length/1000.,-width/1000.));

                    }
                    // mat3.multiply(new Matrix3().makeScale(-width/1000.,-length/1000.));
                    // mat3.multiply(new Matrix3().makeRotation(Math.PI/2));
                    v.applyMatrix3(mat3);
                    uvs.setXY(i,v.x,v.y);
                    
                }
            }

            this._solidMesh.name = "Mesh-" + this.name;


            if (this.name === "CRealFurnitureEntity") {
                this._solidMesh.scale.set(1, 1, 1);
                this._solidMesh.position.set(0, 0, height / 2);
                this.scale.set(1, 1, 1);
            } else {
                this._solidMesh.scale.set(1, 1, 1);
                this._solidMesh.position.set(length / 2, -width / 2, height / 2);
                this.scale.set(1, 1, 1);
            }
            

            this.add(this._solidMesh);
        } else {
            this.remove(this._solidMesh);
            this._solidMesh = null;
        }
    }

    /**
     * 检查板件分类
     */
    checkBoardCategory(): void {
        if (this.isSolid) {
            let iter = 4;
            let t_node = this as RawGltfNode;

            let isVisitBoardPartEntity = false;
            let isVisitSwingDoorLeafEntity = false;
            let isVisitHandlePartEntity = false;
            let isVisitCMetalPartEntity = false;
            let isVisitWhModelEntity = false;
            let isVisitDoorBoardEntityBase = false;
            let isDrawerBasket = false;
            let isDrawerDefault = false;

            while (iter-- && t_node) {
                let name = t_node.name;
                let ex_name = t_node?.rawData?.extras?.name || "---";
                
                if (name === "CRealFurnitureEntity") {
                    this.boardCategory = "成品";
                    return;
                }
                if (compareNames([ex_name], ["左侧板", "右侧板", "顶板", "底板", "层板", "平板护墙", "背板", "踢脚板","格栅"])) {
                    this.boardCategory = "柜体";
                    return;
                }
                if(compareNames([ex_name],["假门"]))
                {
                    this.boardCategory = "假门";
                    return;
                }
                if (compareNames([ex_name], ["翻门"])) {
                    this.boardCategory = "翻门";
                    return;
                }
                if (compareNames([ex_name], ["掩门扇"])) {
                    this.boardCategory = "掩门";
                    return;
                }
                if (compareNames([ex_name], ["平板抽面"])) {
                    this.boardCategory = "抽面";
                    return;
                }
                if (compareNames([ex_name], ["拉篮"])) {
                    isDrawerBasket = true;
                }
                if (compareNames([ex_name], ["抽屉"])) {
                    isDrawerDefault = true;
                }
                if (name === "CApplianceModelEntity") {
                    this.boardCategory = "电器";
                    return;
                }
                if (name === "CBoardPartEntity") {
                    isVisitBoardPartEntity = true;
                }
                if (name === "CSwingdoorLeafEntity") {
                    isVisitSwingDoorLeafEntity = true;
                }
                if (name === "CHandlePartEntity") {
                    isVisitHandlePartEntity = true;
                    this.boardCategory = "拉手";
                    return;
                }
                if (name === "CMetalPartEntity") {
                    isVisitCMetalPartEntity = true;
                }
                if (name === "CWhModelEntity") {
                    isVisitWhModelEntity = true;
                }
                if (name === "CDoorBoardEntityBase") {
                    isVisitDoorBoardEntityBase = true;
                }

                t_node = t_node.parent as RawGltfNode;
            }

            if (isVisitHandlePartEntity) {
                this.boardCategory = "拉手";
            } else if (isVisitCMetalPartEntity) {
                this.boardCategory = "五金";
            } else if (isVisitSwingDoorLeafEntity) {
                this.boardCategory = "掩门";
            } else if (isVisitWhModelEntity) {
                this.boardCategory = "成品";
            } 
            else if (isVisitBoardPartEntity) {
                this.boardCategory = "柜体";
            } else {
                this.boardCategory = "其它";
            }
        } else {
            this.boardCategory = "";
        }
    }

    /**
     * 获取边界框
     */
    get bbox3(): Box3 {
        return this.userData.bbox3 || null;
    }

    /**
     * 设置边界框
     */
    set bbox3(bbox: Box3) {
        this.userData.bbox3 = bbox;
    }

    /**
     * 更新边界框
     */
    public updateBox3(force: boolean = false): Box3 {
        if (!this.bbox3 || force) {
            let bbox = new Box3();
            bbox.setFromObject(this);
            this.bbox3 = bbox;
        }
        return this.bbox3;
    }
} 