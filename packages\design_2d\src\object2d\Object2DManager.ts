import { DomainApiHub, EntityType, IEntityBase } from "@layoutai/design_domain";
import { TPainter } from "../draw/TPainter";
import { IEventCenter } from "../events/IEventCenter";
import { Furniture2D } from "./Furniture2D";
import { FurnitureGroup2D } from "./FurnitureGroup2D";
import { Object2DBase } from "./Object2DBase";
import { Room2D } from "./Room2D";
import { SubArea2D } from "./SubArea2D";
import { Wall2D } from "./Wall2D";
import { Window2D } from "./Window2D";
import { Door2D } from "./Door2D";
import { DrawingManager } from "../draw/DrawingManager";
import { Design2DContext } from "../Design2DContext";

/**
 * 2D对象管理器
 */
export class Object2DManager {
    private _isInit: boolean = false;
    private _object2DMap: Map<string, Object2DBase> = new Map();
    // 存储2D对象uuid到实体uuid的映射
    private _object2DToEntityMap: Map<string, string> = new Map();
    
    private _entityClassMap: Map<string, typeof Object2DBase> = new Map();
    private _context: Design2DContext;
    private _drawingManager: DrawingManager;
    // 维护显示顺序
    private _displayOrder: Object2DBase[] = [];

    constructor(context: Design2DContext) {
        this._context = context;
        this._drawingManager = new DrawingManager();
    }

    /**
     * 获取事件中心
     */
    private get eventCenter(): IEventCenter | null {
        if (!this._context) {
            console.error("Context not set. Call setContext() first.");
            return null;
        }
        return this._context.eventCenter;
    }

    private get painter(): TPainter | null {
        return this._context?.painter || null;
    }           

    public init(): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        // 初始化 DrawingManager
        if (this.painter) {
            this._drawingManager.init(this.painter);
        }
        
        this._registerEntityObject2D();
        this._clearAllObjects();
    }

    /**
     * 注册实体类型与2D对象的映射关系
     * @param type 实体类型
     * @param object2DClass 2D对象类
     * @returns 如果已存在相同类型的注册，返回false；注册成功返回true
     */
    public registerEntityObject2D(type: string, object2DClass: typeof Object2DBase): boolean {
        if (this._entityClassMap.has(type)) {
            console.warn(`实体类型 ${type} 已经注册过，请勿重复注册`);
            return false;
        }
        this._entityClassMap.set(type, object2DClass);
        return true;
    }

    /**
     * 注销实体类型与2D对象的映射关系
     * @param type 实体类型
     */
    public unregisterEntityObject2D(type: string): boolean {
        return this._entityClassMap.delete(type);
    }

    /**
     * 获取已注册的实体类型列表
     * @returns 已注册的实体类型数组
     */
    public getRegisteredEntityTypes(): string[] {
        return Array.from(this._entityClassMap.keys());
    }

    private _registerEntityObject2D(): void {
        this.registerEntityObject2D(EntityType.wall, Wall2D);
        this.registerEntityObject2D(EntityType.room, Room2D);
        this.registerEntityObject2D(EntityType.subArea, SubArea2D);
        this.registerEntityObject2D(EntityType.furniture, Furniture2D);
        this.registerEntityObject2D(EntityType.furnitureGroup, FurnitureGroup2D);
        this.registerEntityObject2D(EntityType.window, Window2D);
        this.registerEntityObject2D(EntityType.door, Door2D);
    }

    public createObject2DByEntity(entity: IEntityBase): Object2DBase | undefined {
        let cls = this._entityClassMap.get(entity.entityType);
        if (!cls) {
            console.warn(`未找到实体类型 ${entity.entityType} 对应的 2D 对象`);
            return;
        }

        let obj2D = new cls();
        this._object2DMap.set(obj2D.uuid, obj2D);
        this._object2DToEntityMap.set(obj2D.uuid, entity.uuid);
        this.eventCenter?.emit('object2d:created', { entity, obj2D });
        return obj2D;
    }

    /**
     * 通过类型和UUID直接创建2D对象
     * @param type 实体类型
     * @param entityUuid 实体UUID
     * @returns 创建的2D对象
     */
    public createObject2D(type: string, entityUuid?: string): Object2DBase | undefined {
        let cls = this._entityClassMap.get(type);
        if (!cls) {
            console.warn("实体类型未注册显示对象", type);
            return;
        }

        let obj2D = new cls(entityUuid);
        this._object2DMap.set(obj2D.uuid, obj2D);
        if (entityUuid) {
            if(this._object2DToEntityMap.has(obj2D.uuid)) {
                console.warn("2D对象已存在实体UUID，请勿重复设置", obj2D.uuid, entityUuid);
                return;
            }
            this._object2DToEntityMap.set(obj2D.uuid, entityUuid);
        }
        this.eventCenter?.emit('object2d:created', { entityType: type, uuid: entityUuid, obj2D });
        return obj2D;
    }

    /**
     * 获取2D对象对应的实体UUID
     * @param obj2DUuid 2D对象的UUID
     * @returns 实体UUID或undefined
     */
    public getEntityUuidByObject2D(obj2DUuid: string): string | undefined {
        return this._object2DToEntityMap.get(obj2DUuid);
    }

    /**
     * 获取实体对应的2D对象
     * @param entityUuid 实体UUID
     * @returns 2D对象或undefined
     */
    public getObject2DByEntityUuid(entityUuid: string): Object2DBase | undefined {
        for (const [obj2DUuid, eUuid] of this._object2DToEntityMap.entries()) {
            if (eUuid === entityUuid) {
                return this._object2DMap.get(obj2DUuid);
            }
        }
        return undefined;
    }

    /**
     * 获取2D对象
     * @param uuid 2D对象的UUID
     * @returns 2D对象或undefined
     */
    public getObject2D(uuid: string): Object2DBase | undefined {
        return this._object2DMap.get(uuid);
    }

    /**
     * 移除2D对象
     * @param uuid 2D对象的UUID
     * @returns 是否移除成功
     */
    public removeObject2D(uuid: string): boolean {
        const obj2D = this._object2DMap.get(uuid);
        const removed = this._object2DMap.delete(uuid);
        this._object2DToEntityMap.delete(uuid);
        if (removed && obj2D) {
            this.eventCenter?.emit('object2d:removed', { uuid, obj2D });
        }
        return removed;
    }

    public getAllObjects2D(): Object2DBase[] {
        return Array.from(this._object2DMap.values());
    }

    /**
     * 获取按显示顺序排列的对象列表
     */
    public getOrderedObjects(): Object2DBase[] {
        // 如果没有维护显示顺序，则返回Map中的所有对象
        if (this._displayOrder.length === 0) {
            return Array.from(this._object2DMap.values());
        }
        return [...this._displayOrder];
    }

    /**
     * 添加对象时更新显示顺序
     */
    public addObject(obj: Object2DBase) {
        this._object2DMap.set(obj.uuid, obj);
        if (!this._displayOrder.includes(obj)) {
            this._displayOrder.push(obj);
        }
    }

    /**
     * 移除对象时更新显示顺序
     */
    public removeObject(obj: Object2DBase) {
        this._object2DMap.delete(obj.uuid);
        const index = this._displayOrder.indexOf(obj);
        if (index !== -1) {
            this._displayOrder.splice(index, 1);
        }
    }

    public updateObject2D(): void {        
        let curEntityUUIDs: Map<string, boolean> = new Map();
        let entities = DomainApiHub.instance.getAllEntities();
        this._drawingManager.clearCommands();
        
        // 处理基于实体的对象
        for (let entity of entities) {
            let obj2D = this.getObject2DByEntityUuid(entity.uuid);
            if (!obj2D) {
                obj2D = this.createObject2D(entity.entityType, entity.uuid);
            }
            if (obj2D) {
                obj2D.update();
                if (this.painter) {
                    this._registerObjectRenderCommands(obj2D);
                }
            }
            curEntityUUIDs.set(entity.uuid, true);
        }

        // 处理直接创建的非实体对象（如PreviewRect2D）
        for (let [uuid, obj2D] of this._object2DMap) {
            const entityUuid = this._object2DToEntityMap.get(uuid);
            if (!entityUuid || !curEntityUUIDs.has(entityUuid)) {
                // 这是一个非实体对象或实体不存在，直接更新和渲染
                obj2D.update();
                if (this.painter) {
                    this._registerObjectRenderCommands(obj2D);
                }
            }
        }

        if (this.painter) {
            this._drawingManager.executeCommands(this.painter);
        }

    }

    private _registerObjectRenderCommands(obj: Object2DBase): void {
        if (!this.painter) return;
        obj.registerRenderCommands(this._drawingManager);
    }

    public clearAllObjects(): void {
        this._clearAllObjects();
    }

    private _clearAllObjects(): void {
        this._object2DMap.clear();
        this._object2DToEntityMap.clear();
        this.eventCenter?.emit('object2d:cleared');
    }

    public dispose(): void {
        this._clearAllObjects();
        this._drawingManager.dispose();
        this._isInit = false;
    }
}