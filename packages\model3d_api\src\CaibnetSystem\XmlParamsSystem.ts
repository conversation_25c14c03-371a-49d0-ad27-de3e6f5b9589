import { XmlEntityBase } from "./XmlEntityBase";
import { XmlSpace } from "./XmlEntities/XmlSpace";
import { XmlStdWardrobeEntity } from "./XmlEntities/XmlStdWardrobeEntity";
import {  I_BoardPose, I_SpacePose, cloneSpacePose, fillBoardPose, fillSpacePose, getBoardPoseDataBySpacePose, getObjectFromNumberVariables, isEqualsBoardPose, isEqualsSpacePose } from "./IWardrobeEntityInterface";
import { NumberVariable } from "./NumberVariable";
import {FormulaParser} from "./FormulaParser/FormulaParser"
import { Euler, Vector3 } from "three";
import { XmlCBoardPartEntity } from "..";


/**
 *  自由柜驱动的参数化系统
 */
export class XmlParamsSystem
{
    protected _xmlSpaces : Map<string,XmlSpace>;
    protected _stdEntities : Map<string,XmlEntityBase>

    protected _rootEntity : XmlEntityBase = null;
    protected _formulas : Map<string, {Variable:NumberVariable, formula?:FormulaParser}>;
    static RootKey = "Root";

    constructor()
    {
        this._xmlSpaces = new Map();
        this._stdEntities = new Map();
        this._formulas = new Map();
    }
    clear()
    {
        this._xmlSpaces.clear();
        this._stdEntities.clear();
        this._formulas.clear();
        this._rootEntity = null;
    }

    bindWardrobeEntity(EntityBase:XmlEntityBase,isRoot:boolean = true)
    {
        let entity = EntityBase as XmlStdWardrobeEntity;
        if(entity?.Children?.CWhSpaceEntity)
        {
            this.bindSpace(entity.Children.CWhSpaceEntity[0]);
        }
        if(isRoot)
        {
            this._rootEntity = EntityBase;
        }
        entity.updateValueObject();
        this.registerEntity(EntityBase,isRoot);
        if(entity.CWhSpacePartComponent?.splitTypeN)
        {
            let space = this.getSpaceByUid(''+entity.CWhSpacePartComponent.spaceUidN);
            if(space)
            {
                if(!space.attachedBoardEntities.includes(entity))
                {
                    space.attachedBoardEntities.push(entity);
                }
            }
        }
        
        if(entity.Children)
        {
            for(let key in entity.Children)
            {
                if(entity.Children[key] === entity.Children.CWhSpaceEntity)
                {
                    continue;
                }
                if(Array.isArray(entity.Children[key]))
                {
                    entity.Children[key].forEach((child)=>{
                        this.bindWardrobeEntity(child,false);
                    })
                }
            }
        }

    }   

    // 中-左-右 的遍历顺序
    bindSpace(space : XmlSpace)
    {
        if(!space) return;
        this.registerSpace(space);
        if(space.childSpaces)
        {
            space.childSpaces.forEach((space)=>this.bindSpace(space));
        }

    }

    registerSpace(space:XmlSpace)
    {
        this._xmlSpaces.set(''+space.uidN,space);
        let entity = space;
        if(entity?.VariableComponet?.NumberVariable)
        {
            if(Array.isArray(entity.VariableComponet.NumberVariable))
            {
                entity.VariableComponet.NumberVariable.forEach((numVar,index)=>{
                    let key = ''+space.uidN+"."+(numVar.nameS||"---");
                    let valueExpressionS = numVar.valueExpressionS;
                    if(valueExpressionS && !FormulaParser.isNumeric(valueExpressionS))
                    {
                        this._formulas.set(key,{
                            Variable : numVar,
                            formula : new FormulaParser(numVar.valueExpressionS).autoTest() 
                        });
                    }
                    else{
                        // this._formulas.set(key,{
                        //     Variable : numVar
                        // });
                    }

                })
            }
        }
    }
    registerEntity(entity:XmlEntityBase,isRoot:boolean = false)
    {
        let uidS = this.getUidOfEntity(entity);
        this._stdEntities.set(uidS,entity);
        if(entity?.VariableComponet?.NumberVariable)
        {
            if(Array.isArray(entity.VariableComponet.NumberVariable))
            {
                entity.VariableComponet.NumberVariable.forEach((numVar,index)=>{
                    let key = uidS+"."+(numVar.nameS||"---");
                    let valueExpressionS = numVar.valueExpressionS;
                    if(valueExpressionS && !FormulaParser.isNumeric(valueExpressionS))
                    {
                        this._formulas.set(key,{
                            Variable : numVar,
                            formula : new FormulaParser(numVar.valueExpressionS).autoTest()
                        });
                    }
                    else{
                        // this._formulas.set(key,{
                        //     Variable : numVar
                        // });
                    }

                })
            }
        }
    }
    getUidOfEntity(entity : XmlEntityBase)
    {
        let uidS= entity.uidN?(''+entity.uidN):(entity.uidS||'');
        if(uidS.length == 0)
        {
            if(entity === this._rootEntity)
            {
                uidS = XmlParamsSystem.RootKey;
            }
        }
        return uidS;
    }
    getEntityByUid(uid:string)
    {
        return this._stdEntities.get(uid) || null;
    }

    getSpaceByUid(uid:string)
    {
        return this._xmlSpaces.get(uid) || null;
    }
    computeVisibleExpS(visibleExpS:string,  context:{values:{[key:string]:number|string}}={values:{}})
    {
        let t : boolean = true;
        try {
            t = new Function(...Object.keys(context.values),`return ${visibleExpS}`)(...Object.values(context.values));

        } catch (error) {
            console.log(t, {...context.values},visibleExpS);
        }
        return t;
    }
    checkVisibleB(entity:XmlEntityBase = null, context:{values:{[key:string]:number|string}}={values:{}})
    {
        if(!entity)
        {
            entity = this._rootEntity;
        }
        if(!entity) return;
        let visibleResult = true;
        let srcValueObj = {...context.values};
        if(entity.visibleExpS)
        {
            let visibleExpS = entity.visibleExpS;
            let tt = this.computeVisibleExpS(visibleExpS,context);
            if(tt !== entity.visibleB)
            {
                // console.log(tt, {...context.values},visibleExpS);
            }
        }
        this.checkEntity(entity);

        context.values = {...context.values,...entity.valuesObj}
        if(entity.Children)
        {
            let t_entity = entity as XmlStdWardrobeEntity;
            if(t_entity.Children.CWhSpaceEntity)
            {
                t_entity.Children.CWhSpaceEntity.forEach((child)=>{
                    this.checkVisibleB(child,context);

                    this.chaeckSpace(child,context)
                });

            }
            for(let key in entity.Children)
            {
                if(entity.Children[key] === t_entity.Children.CWhSpaceEntity)
                {
                    continue;
                }
                if(entity.Children[key] === t_entity.Children.CRealFurnitureEntity)
                {
                    continue;
                }
                entity.Children[key].forEach((child)=>{
                    this.checkVisibleB(child,context);
                })
            }
        }
        context.values = {...srcValueObj}; // 递归复原处理
    }


    chaeckSpace(entity:XmlSpace,context:{values:{[key:string]:number|string}}={values:{}})
    {
        let valueObj = entity.valuesObj as I_SpacePose;
        let entityPose = fillSpacePose(valueObj);

        let childSpaces = entity.childSpaces || [];
        let splitUnit = entity.splitUnit;
        let splitValues = entity.splitValues;
        let splitDirN = entity.splitDirN || 0;
        let expands = entity.expands;


    
        let templateTypeN = entity.templateTypeN;
        let hasSplitCompentBoard = entity.CWhSpaceSplitComponent?.SubNodeCWhSpaceSplit ? true:false;
        const thickness = 18;

        // splitDirN == 4 主要是Arround补板
        // splitDirN == 0, 应该是没有子空间的
        // splitDirN == 1, 按照splitVList,  [W0,W1,W2,W3],[H,H,H,H], 垂直切分
        // splitDirN == 3, 按照splitUVlist  [W,W,W,W],[H0,H1,H2,H3], 水平切分 

        let arroundBoardSpaces = entity.updateArroundBoardSpaces();
        let spaces = arroundBoardSpaces[0].spacePose;
        let currentPose = spaces[0];
        if(splitDirN === 4)
        {
            if(childSpaces[0])
            {
                let notEqualKeys : string[] = [];
                let isEquals = isEqualsSpacePose(currentPose, childSpaces[0].valuesObj as I_SpacePose,notEqualKeys);
                if(!isEquals)
                {
                    console.log("Arround NotEquals childSpaces[0]",currentPose,childSpaces[0].valuesObj, notEqualKeys,entity);
                }
                else{
                    // console.log("Arround Equals childSpaces[0]",currentPose,childSpaces[0].valuesObj, notEqualKeys,entity);
  
                }
            }
            if(childSpaces[1] && spaces[1])
            {
                let notEqualKeys : string[] = [];
                let isEquals = isEqualsSpacePose(spaces[1], childSpaces[1].valuesObj as I_SpacePose,notEqualKeys);
                if(!isEquals)
                {
                    console.log("Arround NotEquals childSpaces[1]",spaces[1],childSpaces[1].valuesObj,notEqualKeys,entity);
                }
                else{
                    // console.log("Arround Equals childSpaces[1]",spaces[1],childSpaces[1].valuesObj, notEqualKeys,entity);

                }
            }


            
        }
        else {
            // console.log({splitDirN, splitUnit,splitVList,splitUList},childSpaces.map((e)=>e.valuesObj));
            // 先初始化

            let W_list : number[] =  splitUnit.map((xx)=>currentPose.W);
            let PX_list : number[] =  splitUnit.map((xx)=>currentPose.PX);
            let H_list : number[] =  splitUnit.map((xx)=>currentPose.H)
            let PZ_list : number[] = splitUnit.map((xx)=>currentPose.PZ);
            let D_list : number[] = splitUnit.map((xx)=>currentPose.D);
            let PY_list : number[] = splitUnit.map((xx)=>currentPose.PY);


            if(splitDirN == 1 || splitDirN == 3 || splitDirN == 2)
            {
                let fixedSums = 0;
                let weightsSums = 0;
                splitUnit.forEach((unit,index)=>{
                    
                    if(unit == 0 || Math.abs(unit) < 1e-2)  // 等于0
                    {
                        fixedSums += splitValues[index] || 0;
                    }
                    else if(unit==1 || Math.abs(unit - 1)<1e-2)
                    {
                        weightsSums += splitValues[index] || 0;
                    }
                    if(hasSplitCompentBoard && index>=1)
                    {
                        fixedSums += thickness;
                    }
                });
                let totalVals = {1:currentPose.W,2:currentPose.D,3:currentPose.H};
                let lengthLists = {1:W_list,2:D_list,3:H_list};
                let posLists = {1:PX_list,2:PY_list,3:PZ_list};
                
                let totalVal =  totalVals[splitDirN];
                // 对应不同的 长度 、 位置
                let Length_List = lengthLists[splitDirN]
                let Pos_List = posLists[splitDirN];


                let totalSum = totalVal - fixedSums;
                let currentVal = Pos_List[0] || 0;
                let dirFlag = splitDirN == 2?-1:1;

                splitUnit.forEach((unit,index)=>{
                    Length_List[index] = 0;
                    currentVal +=  ((index > 0 && hasSplitCompentBoard)?thickness:0);
                    Pos_List[index] = currentVal*dirFlag;
                    if(unit == 0 || Math.abs(unit) < 1e-2)  // 等于0
                    {
                        Length_List[index] = splitValues[index] || 0;
                    }
                    else if(unit==1 || Math.abs(unit - 1)<1e-2)
                    {
                        Length_List[index] = ((splitValues[index]||0)/weightsSums * totalSum);
                    }
                    currentVal += Length_List[index];                    
                })
            }
            else if(splitDirN !== 0)
            {
                console.log("找到splitDirN 不为[0,1,2,3,4]的参数")
            }

            splitUnit.forEach((unit,index)=>{
                let childPose : I_SpacePose = {...currentPose, 
                    PX : PX_list[index],
                    PY : PY_list[index],
                    PZ : PZ_list[index],
                    W : W_list[index],
                    H : H_list[index],
                    D : D_list[index]
                };

                if(childSpaces[index])
                {
                    let childValsObj = childSpaces[index].valuesObj;

                    let notEqualKeys : string[] = [];
                    let isEquals = isEqualsSpacePose(childPose,childValsObj as I_SpacePose,notEqualKeys);
                    if(!isEquals)
                    {
                        console.log("NotEquals childSpaces",index,JSON.stringify(childPose),JSON.stringify(childValsObj),notEqualKeys,
                        entity.uidN,splitDirN,splitValues, entity);
                    }
                    else{
                        // console.log("Equals childSpaces",index,childPose,childValsObj,entity.uidN,entity);
                    }

                }


            })

        }

        if(childSpaces)
        {
            childSpaces.forEach((child)=>this.chaeckSpace(child,context));
        }
    }

    checkEntity(entity:XmlEntityBase,context:{values:{[key:string]:number|string}}={values:{}})
    {
        
        if(entity.valuesObj.BUM) {
            let boardPose = fillBoardPose( {...entity.valuesObj} as I_BoardPose);
    
            let boardPose1 = getBoardPoseDataBySpacePose(boardPose as I_SpacePose);
    
            let notEqualKeys :string[] = [];
            let isEquals = isEqualsBoardPose(boardPose,boardPose1,notEqualKeys);
    
            if(!isEquals)
            {
                console.log("板件默认参数BLM等计算错误",notEqualKeys);
            }
        }
        const boardThickness0 = 18;
        if(entity.CWhSpacePartComponent?.spaceUidN)
        {
            // console.log(this.getSpaceByUid(''+entity.CWhSpacePartComponent.spaceUidN)?.valuesObj,cloneSpacePose( entity.valuesObj),getObjectFromNumberVariables(entity?.CWhSpacePartComponent?.SpaceVariableComponet?.NumberVariable||[]));
            let spacePose = getObjectFromNumberVariables(entity?.CWhSpacePartComponent?.SpaceVariableComponet?.NumberVariable||[]) as I_SpacePose;
            let xmlSpace = this.getSpaceByUid(''+entity.CWhSpacePartComponent.spaceUidN);
            let parentSpacePose = xmlSpace?.valuesObj;

            let spacePlaceRuleS = entity.CWhSpacePartComponent.spacePlaceRuleS;
            let spacePlaceRule = spacePlaceRuleS.split(",");
            let splitTypeN = entity.CWhSpacePartComponent.splitTypeN;

            let boardThickness = entity.valuesObj.H as number;

            let depthExpand = 0;
            if(spacePlaceRule[8])
            {
                let val = parseFloat(spacePlaceRule[8]);
                if(isNaN(val)) val = 0;
                if(Math.abs(val) > 1e-1)
                {
                    depthExpand = val;
                }
            }
            if(splitTypeN == 1)
            {
                let c_pose : I_SpacePose= fillSpacePose({} as any as I_SpacePose);
                let p_pose = fillSpacePose(parentSpacePose as I_SpacePose);
                let arroundPose = (xmlSpace.arroundBoardSpaces[splitTypeN]?.spacePose || [])[0] || p_pose;
                c_pose.W = arroundPose.H;
                c_pose.D = arroundPose.D - depthExpand;
                c_pose.H = boardThickness;
                c_pose.PX = arroundPose.PX + arroundPose.W;
                c_pose.PY = 0;
                c_pose.PZ = arroundPose.PZ;;
                c_pose.RX = 0;
                c_pose.RY = -90;
                c_pose.RZ = 0;
                if(!isEqualsSpacePose(c_pose,spacePose))
                {
                    console.log(`splitTypeN==${splitTypeN}`,c_pose,spacePose,arroundPose,xmlSpace);
                }            
            }
            else if(splitTypeN== 2)
            {
                let c_pose : I_SpacePose= fillSpacePose({} as any as I_SpacePose);
                let p_pose = fillSpacePose(parentSpacePose as I_SpacePose);
                let arroundPose = (xmlSpace.arroundBoardSpaces[splitTypeN]?.spacePose || [])[0] || p_pose;
                c_pose.W = arroundPose.H;
                c_pose.D = arroundPose.D - depthExpand;
                c_pose.H = boardThickness;
                c_pose.PX = boardThickness;
                c_pose.PY = 0;
                c_pose.PZ = arroundPose.PZ;;
                c_pose.RX = 0;
                c_pose.RY = -90;
                c_pose.RZ = 0;
                if(!isEqualsSpacePose(c_pose,spacePose))
                {
                    console.log(`splitTypeN==${splitTypeN}`,c_pose,spacePose,entity);
                }                      
            }
            else if(splitTypeN == 5) // 顶板
            {
                let c_pose : I_SpacePose= fillSpacePose({} as any as I_SpacePose);
                let p_pose = fillSpacePose(parentSpacePose as I_SpacePose);
                let arroundPose = (xmlSpace.arroundBoardSpaces[splitTypeN]?.spacePose || [])[0] || p_pose;
                c_pose = {...arroundPose};
                c_pose.D -= depthExpand;

                if(!isEqualsSpacePose(c_pose,spacePose))
                {
                    console.log(`splitTypeN==${splitTypeN}`,c_pose,spacePose,entity, spacePlaceRule);
                }        
            }
            else if(splitTypeN == 6) // 底板
            {
                let c_pose : I_SpacePose= fillSpacePose({} as any as I_SpacePose);
                let p_pose = fillSpacePose(parentSpacePose as I_SpacePose);
                let arroundPose = (xmlSpace.arroundBoardSpaces[splitTypeN]?.spacePose || [])[0] || p_pose;
                c_pose = {...arroundPose};

                if(!isEqualsSpacePose(c_pose,spacePose))
                {
                    console.log(`splitTypeN==${splitTypeN}`,c_pose,spacePose);
                }        
            }
            else if(splitTypeN == 4){
                let c_pose : I_SpacePose= fillSpacePose({} as any as I_SpacePose);
                let p_pose = fillSpacePose(parentSpacePose as I_SpacePose);
                let arroundPose = (xmlSpace.arroundBoardSpaces[splitTypeN]?.spacePose || [])[0] || p_pose;

                let t_py = parseFloat(spacePlaceRule[9] || "0");
                if(isNaN(t_py))
                {
                    t_py = 0;
                }
            
                c_pose.W = arroundPose.H;
                c_pose.D = arroundPose.W;
                c_pose.H = boardThickness;
                c_pose.PX = arroundPose.PX;
                c_pose.PY = t_py;
                c_pose.PZ = arroundPose.PZ;
                c_pose.RX = 0;
                c_pose.RY = -90;
                c_pose.RZ = 90;
                if(!isEqualsSpacePose(c_pose,spacePose))
                {
                    console.log(`splitTypeN==${splitTypeN}`,c_pose,spacePose,arroundPose,entity);
                }   
            }
            else if(splitTypeN == 9)
            {
                
            }
            else {
                // console.log(splitTypeN,spacePlaceRuleS,parentSpacePose,spacePose)
            }


        }
        else{
            let entitiyNames = ["CWhSpaceEntity","CWardrobeGroupEntity","CWhSpaceEntity","CDoorBoardEntityBase","CSwingdoorLeafEntity",'CFaceBoardEntity','CCabinetLightEntity',
            'CMetalPartEntity','背拉条']
            if(entity.visibleB)
            {
                if(!entitiyNames.includes(entity.EntityName) && !entitiyNames.includes(entity.nameS))
                {
                    // console.log("没有spaceUid", entity);
                }

            }
        }



        
    }

    

}