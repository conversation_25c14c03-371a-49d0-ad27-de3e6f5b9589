import { I_DesignMaterialInfo, StyleBrush, StyleComposeItem } from "@layoutai/basic_data";
import { compareNames } from "@layoutai/z_polygon";
import { getImgDomain } from "@svg/request";
import { Color, Float32BufferAttribute, Group, Mesh, MeshStandardMaterial, Vector3, Vector3Like } from "three";
import { GLTFLoader } from "three/examples/jsm/Addons.js";
import { DesignMaterialService, MaterialManager, SvgGltfSwingDoorLeafNode } from "..";
import { SvgGltfBoardBatchNode, SvgGltfFurnitureNode, SvgGltfNode } from "../GltfNode";
import { SvgGltfObjectNode } from "../GltfNode/SvgGltfObjectNode";
import { UserDataKey } from "../NodeName";
import { TextureManager } from "../TextureManager";
import { I_CustomCabinetInfoItem, LayoutCustomCabinetInfoService } from "../services/LayoutCustomCabinet/LayoutCustomCabinetInfoService";
import { CabinetStyleService } from "../services/Materials/CabinetStyleService";
import { GlbLoaderConfig } from "./GlbLoaderConfig";

export interface I_CustomCabinetInfoItemEx extends I_CustomCabinetInfoItem
{
    gltf_group ?: Group;
}
export class GltfManager {

    private static _glbUrlCache = new Map<string, I_CustomCabinetInfoItemEx[]>();
    /**
    * 修复部分网格的uv问题
    * @param mesh  
    */
    static _refineMeshUVs(mesh: Mesh) {
        let geometry = mesh.geometry;
        let uv = geometry.attributes.uv;
        if (!uv || Number.isNaN(uv.array[0])) {
            // 需要重新更新uv
            let position = geometry.attributes.position;
            if (position) {
                let uvs = new Array<number>(position.count * 2);
                for (let i = 0; i < position.count; i++) {
                    uvs[2 * i + 0] = position.getX(i) / 1000.;
                    uvs[2 * i + 1] = position.getY(i) / 1000.;
                }
                geometry.setAttribute("uv", new Float32BufferAttribute(uvs, 2));
            }

        }
    }

    /**
     * 修复部分网格的emissive强度问题
     * @param mesh   
     */
    static _refineMaterialEmissive(mesh: Mesh) {
        let material = mesh.material as MeshStandardMaterial;

        // emissiveIntensity过强, 颜色就可能无效了
        if (material && material?.emissiveIntensity && material.emissiveIntensity > 0.8) {
            if(!material.transparent) 
            {
                if(material.color)
                {
                    if(material.color.r==0 && material.color.g == 0 && material.color.b == 0)
                    {
                        material.color =new Color(0xffffff);
                    }
                }
            }

            if (material.color) {
                material.emissive = material.color.clone();
            }

        }
    }
    static async getGltfObjectByUrl(glbUrl: string, options:{needsSvgObject?:boolean}={}) {
        let response = await fetch(glbUrl);

        if (response.ok) {
            // 获取二进制数据
            const arrayBuffer = await response.arrayBuffer();

            if (!arrayBuffer || arrayBuffer.byteLength === 0) {
                console.error(`GLB文件为空: ${glbUrl}`);
                return null;
            }

            const gltfLoader = new GLTFLoader();

            // 使用二进制数据解析GLB文件
            const gltf = await gltfLoader.parseAsync(arrayBuffer, glbUrl);
            if (!gltf || !gltf.scene) {
                console.warn(`GLB文件解析失败或场景为空: ${glbUrl}`);
                return null;
            }

            if (!gltf.scene.children || gltf.scene.children.length === 0) {
                console.warn(`GLB场景中没有子节点: ${glbUrl}`);
                return null;
            }
            const result = gltf.scene.children[0] as Group;

            if (!result) {
                console.warn(`GLB场景的第一个子节点不是Group类型: ${glbUrl}`);
                return null;
            }
            result.traverseVisible((child) => {
                let mesh = child as Mesh;
                if (mesh.isMesh) {
                    GltfManager._refineMeshUVs(mesh);
                    GltfManager._refineMaterialEmissive(mesh);
                }
            });

            result.userData.glbUrl = glbUrl;
            // 将加载的结果存储到缓存中
            // GlbCache.set(cleanMaterialId, result);
            if(options?.needsSvgObject)
            {
                return new SvgGltfObjectNode().fromObject3D(result).resetMatrix(); // 返回克隆对象，避免修改缓存中的原始对象
            }
            else{
                return result;
            }
        }
        return null;

    }
    /**
     * 根据材质ID获取GLB文件
     * @param materialId 材质ID
     * @returns 
     */
    static async getGltfObjectByMaterialId(materialId: string, defaultLength: number, defaultWidth: number, defaultHeight: number, targetLength: number = 0, targetWidth: number = 0, targetHeight: number = 0): Promise<Group | null> {
        let cleanMaterialId: string = GlbLoaderConfig.sanitizeMaterialId(materialId);

        defaultLength = Math.floor(defaultLength);
        defaultWidth = Math.floor(defaultWidth);
        defaultHeight = Math.floor(defaultHeight);
        targetLength = targetLength || defaultLength;
        targetWidth = targetWidth || defaultWidth;
        targetHeight = targetHeight || defaultHeight;
        try {
            let item = await GltfManager.querySimilarGlbItem(cleanMaterialId,{width:targetLength,height:targetHeight,depth:targetWidth},{width:0.5,depth:0.1,height:1});
            if(item)
            {
                if(item.gltf_group)
                {
                    return new SvgGltfObjectNode().fromObject3D(item.gltf_group).resetMatrix(); // 返回克隆对象，避免修改缓存中的原始对象
                }
                let glbUrl = item.valueUrl;
                if (!glbUrl.startsWith("http")) {
                    glbUrl = "https://3vj-content.3vjia.com/" + glbUrl;
                }

                let group = await this.getGltfObjectByUrl(glbUrl);
                if(group)
                {
                    item.gltf_group = group;
                    return new SvgGltfObjectNode().fromObject3D(item.gltf_group).resetMatrix();
                }
                else{
                    console.warn("加载glb url地址错误", glbUrl);
                    return null;
                }
            }

        } catch (error) {
            console.error(`加载GLB文件失败: materialId=${materialId}`, error);
            // 提供更详细的错误信息
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    console.error(`GLB文件加载超时: materialId=${materialId}`);
                } else if (error.message.includes('404')) {
                    console.error(`GLB文件不存在: materialId=${materialId}`);
                } else if (error.message.includes('network') || error.message.includes('fetch')) {
                    console.error(`网络连接失败: materialId=${materialId}`);
                } else if (error.message.includes('parse') || error.message.includes('format') || error.message.includes('JSON')) {
                    console.error(`GLB文件格式错误: materialId=${materialId}`);
                }
            }

            return null;
        }
    }

    static async querySimilarGlbItem(materialId:string, targetSize:{width:number,depth:number,height:number},weights:{width:number,depth:number,height:number}={width:0.5,depth:0.5,height:1})
    {
        let items : I_CustomCabinetInfoItemEx[] = [];
        if(GltfManager._glbUrlCache.get(materialId))
        {
            items = GltfManager._glbUrlCache.get(materialId);
        }
        else{
            items = await LayoutCustomCabinetInfoService.listByPage({materialId:materialId,styleId:"0"});
            GltfManager._glbUrlCache.set(materialId,items);
        }

        let sort_score = (item:I_CustomCabinetInfoItem)=>{
            let d_width = item.width - targetSize.width;
            let d_depth = item.depth - targetSize.depth;
            let d_height = item.height - targetSize.height;

            return Math.abs(d_width) * weights.width + Math.abs(d_depth) * weights.depth + Math.abs(d_height) * weights.height;
        }
        items.sort((a,b)=>sort_score(a)-sort_score(b));

        // console.log(materialId,targetSize,items[0]?.width,items[0]?.depth,items[0]?.height);
        return items[0] || null;
    }
    public static async updateCabinet3DModelWithStyleBrush(group_node: Group, cabinetStyleId: string, options: { styleBrush?: StyleBrush, category?: string } = {}) {
        let styleBrush = options.styleBrush || null;
        if (!styleBrush) {
            styleBrush = await CabinetStyleService.getStyleBrush(cabinetStyleId);
        }
        cabinetStyleId = cabinetStyleId || options.styleBrush?.id;
        await CabinetStyleService.preloadTexturesFromCabinetStyle(cabinetStyleId);

        // 收集所有需要替换的板件素材信息
        let geometryReplacementItems: { materialId: string; type: string }[] = [];

        // 分析风格刷中的几何体替换项
        styleBrush.items.forEach(item => {
            if (!item.name.includes("材质")) {
                // 根据名称匹配确定目标板件类型
                if (compareNames([item.name], ["翻门", "掩门", "假门", "抽面"])) {
                    geometryReplacementItems.push({
                        materialId: item.materialId,
                        type: item.name
                    });
                }
            }
        });

        // 如果有几何体替换项，先获取所有相关的设计材质信息
        let materialIds = geometryReplacementItems.map(item => item.materialId);
        let designMaterialInfos: I_DesignMaterialInfo[] = [];
        if (materialIds.length > 0) {
            designMaterialInfos = await DesignMaterialService.getDesignMaterialInfoByIds(materialIds);
        }

        // 创建材质ID到设计材质信息的映射
        let materialInfoMap: { [key: string]: I_DesignMaterialInfo } = {};
        designMaterialInfos.forEach(info => {
            materialInfoMap[info.MaterialId] = info;
        });

        // 收集需要替换的mesh及其对应的替换信息
        let replacementTasks: { component: Group; materialId: string; designMaterialInfo: I_DesignMaterialInfo }[] = [];
        let svgGltfDoorLeafNodes: SvgGltfSwingDoorLeafNode[] = [];
        let svgGltfBatchNodes: SvgGltfBoardBatchNode[] = [];

        group_node.traverseVisible(node => {
            if ((node as SvgGltfSwingDoorLeafNode).isSvgGltfSwingDoorLeafNode) {
                svgGltfDoorLeafNodes.push(node as any);
            }
            if ((node as SvgGltfBoardBatchNode).isSvgGltfBoardBatchNode) {
                svgGltfBatchNodes.push(node as any);
            }
        });
        let styleItems = [...styleBrush.items];

        // 掩门 or 翻门 之类的组件
        svgGltfDoorLeafNodes.forEach((componentNode) => {

            if (componentNode.category && !compareNames([componentNode.category], ["翻门", "掩门", "假门", "抽面"])) {
                return;
            }

            let styleFitScore: { id: number, score: number }[] = [];

            styleItems.forEach((item, index) => {
                styleFitScore.push({
                    id: index,
                    score: GltfManager.fitStyleElementScore(componentNode, item, ["材质", "拉手"])
                })
            });
            styleFitScore.sort((a, b) => b.score - a.score);

            let targetItem = styleItems[styleFitScore[0].id];
            let U = componentNode.toBoardData();

            if (targetItem.name.includes("材质")) return;

            const materialId = targetItem.materialId;
            const designMaterialInfo = materialInfoMap[materialId];

            if (designMaterialInfo) {
                const targetGroup = componentNode;
                replacementTasks.push({
                    component: targetGroup,
                    materialId: materialId,
                    designMaterialInfo: designMaterialInfo
                });
                //console.info("updateCabinet3DModelWithStyleBrush() componentNode.name=" + componentNode.name + "  geometryReplacementItem.type=" + geometryReplacementItem?.type + " designMaterialInfo.name=" + designMaterialInfo.MaterialName);
            }
        });


        // 执行Group替换
        for (const task of replacementTasks) {
            try {

                if(task.component instanceof SvgGltfSwingDoorLeafNode)
                {
                   await GltfManager.replaceSwingDoorLeafMaterialId(task.component,task.materialId,{});
                }
            } catch (error) {
                console.error(`替换Group时发生错误: ${task.component.name || task.component.type}`, error);
            }
        }

        let allNodes: SvgGltfNode[] = [...svgGltfDoorLeafNodes, ...svgGltfBatchNodes];

        allNodes.forEach((node) => {

            let styleFitScore: { id: number, score: number }[] = [];
            if (node.category && compareNames([node.category], ["柜体", "抽面", "掩门", "翻门", "假门"])) {
                let styleItem = styleBrush.items.find((item) => item.name.includes("材质") && compareNames([item.name], [node.category]));

                if (styleItem) {
                    let materialId = styleItem.materialId;
                    if (materialId) {
                        node.traverseVisible(async (child) => {
                            let mesh = child as Mesh;
                            if (!mesh.isMesh) return;
                            if(mesh.userData[UserDataKey.IsNotBoardMaterial])
                            {
                                return;
                            }
                            // if(!(mesh.userData.materialMapVoId))
                            // {
                            //     return;
                            // }
                            if (!mesh.userData[UserDataKey.key_standard_material]) {
                                MaterialManager.bindMeshMaterials(mesh, { color_as_standard: false });
                            }
                            // console.log(node.name,mesh);
                            // let material = mesh.material as MeshStandardMaterial;
                            await TextureManager.updateMeshTextureWithImg(mesh, getImgDomain() + styleItem.materialImgPath, materialId, {
                                category: "Cabinet",
                                cabinetStyleId: cabinetStyleId, picWidth: styleItem.picWidth, picHeight: styleItem.picHeight
                            });
                        });
                        node.materialMapVoId = materialId;
                    }
                }
            }
        })
        // group_node.traverseVisible(async (node)=>{
        //     let mesh = (node as Mesh);
        //     if(!mesh.isMesh) return;

        //     if(node.name && compareNames([node.name],["柜体","抽面","掩门","翻门"]))
        //     {
        //         let styleItem = styleBrush.items.find((item)=>item.name.includes("材质") && compareNames([item.name.replace("材质","")],[node.name]));

        //         if(styleItem)
        //         {
        //             let materialId = styleItem.materialId;
        //             if(materialId)
        //             {
        //                 if(node.parent && (node.parent as SvgGltfBoardBatchNode).isSvgGltfBoardBatchNode)
        //                 {
        //                     // console.log(node.name,styleItem,options.category);
        //                     let boardBatchNode = node.parent as SvgGltfBoardBatchNode;
        //                     boardBatchNode.materialMapVoId = materialId;

        //                     let material =  mesh.userData[UserDataKey.key_standard_material];

        //                 }
        //                 await TextureManager.updateMeshTextureWithImg(mesh,getImgDomain()+styleItem.materialImgPath,materialId,{category:"Cabinet",cabinetStyleId:cabinetStyleId});
        //             }
        //         }
        //     }
        // })
    }

    public static async replaceSwingDoorLeafMaterialId(swingDoorNode:SvgGltfSwingDoorLeafNode,materialId:string,options:{designMaterialInfo?:I_DesignMaterialInfo}={})
    {
        const originalBox = SvgGltfNode.UpdateBox3(swingDoorNode);
        const originalBoxSize = originalBox.getSize(new Vector3());
        const originalBoxPosition = originalBox.getCenter(new Vector3());
        let length = Math.max(originalBoxSize.x, originalBoxSize.y);
        let depth = Math.min(originalBoxSize.x,originalBoxSize.y);
        let height = originalBoxSize.z;
        let newGroup = await GltfManager.getGltfObjectByMaterialId(materialId,length,depth,height,length,depth, height);
        if(newGroup instanceof SvgGltfObjectNode)
        {
            await newGroup.updateTextures({targetNames:["CBarBoardEntity"]});
        } 
        // console.log("替代", newGroup.userData.glbUrl);
        if (swingDoorNode && newGroup?.children && newGroup.children[0]) {
            swingDoorNode.replaceComponent(newGroup.children[0], materialId);

        }
    }
    public static async replaceMaterialMapVoId(group_node:Group, materialId:string, options:{designMaterialInfo?:I_DesignMaterialInfo,imgPath?:string})
    {
        if(!group_node) return;
        let imgPath = options.imgPath;
        let picWidth = 1000;
        let picHeight = 1000;
        if(!imgPath)
        {
            let designMaterialInfo = options.designMaterialInfo;
            if(!designMaterialInfo || designMaterialInfo.MaterialId !== materialId)
            {
                designMaterialInfo = (await DesignMaterialService.getDesignMaterialInfoByIds([materialId]))[0];
                if(!designMaterialInfo)
                {
                    console.warn("找不到designMaterialInfo",materialId);
                    return;
                }
            }
            imgPath = getImgDomain() + designMaterialInfo.ImagePath;
            picWidth = designMaterialInfo.PICWidth;
            picHeight = designMaterialInfo.PICHeight;
        }
        if(!imgPath) return;


        group_node.traverseVisible(async (child)=>{
            let mesh = child as Mesh;
            if (!mesh.isMesh) return;
            if(mesh.userData[UserDataKey.IsNotBoardMaterial])
            {
                return;
            }
            if (!mesh.userData[UserDataKey.key_standard_material]) {
                MaterialManager.bindMeshMaterials(mesh, { color_as_standard: false });
            }
            await TextureManager.updateMeshTextureWithImg(mesh, imgPath, materialId, {category:"Cabinet",
               picWidth: picWidth, picHeight: picHeight
            });
        });
        if(group_node instanceof SvgGltfNode)
        {
            group_node.materialMapVoId = materialId;
        }
    }

    public static async replaceRealFurnitureMaterialId(furniture:SvgGltfFurnitureNode, material_id:string, options:{designMaterialInfo?:I_DesignMaterialInfo,PosAlignment?:Vector3Like,SizeAlignment?:Vector3Like}={})
    {
        furniture.materialId = material_id;
        if(!options.PosAlignment)
        {
            options.PosAlignment = {x:0,y:0,z:0};
        }
        if(!options.SizeAlignment)
        {
            options.SizeAlignment = {x:0,y:0,z:0};
        }
        await furniture.loadAndUpdateSolidMesh(options);
    }

    public static fitStyleElementScore(componentNode: SvgGltfNode, item: StyleComposeItem, ignoreWords: string[] = ["材质"]) {
        if (compareNames([item.name], ignoreWords)) return 0;

        let score = 0;
        if (compareNames([item.name], [componentNode.name, componentNode.category])) {
            score += 10;
        }
        if (score > 0) {
            if (item.applyCondition && item.applyCondition !== "undefined") {
                let U = componentNode.toBoardData();
                try {
                    let res = new Function("U", "H", "W", "D", "return " + item.applyCondition)(U, U.H, U.W, U.D);

                    // console.log(item.name, componentNode.userData?.displayName, item.applyCondition, `U.H`, U.H, `H ${U.H} 计算结果:${res}`);
                    if (res) {
                        score += 10;
                    }
                } catch (error) {
                    console.log(error, item.applyCondition);
                }
            }

            if (componentNode.orientationType) {
                if (compareNames([item.name], [componentNode.orientationType])) {
                    score += 10;
                }
                if (item.name.includes("上开")) {
                    score += 0.5;
                }

            }

        }

        return score;



    }
}