import "./index.less";

import { Box3, Mesh, Object3D, Vector3 } from "three";
import { generateUUID } from "three/src/math/MathUtils.js";

import { Model3dViewer } from "@layoutai/model3d_api";


/**
 * @description 模型节点树
 */
export const Object3dTreeItem: React.FC<{ object: Object3D, prefix: string, level: number }> = (props: { object: Object3D, prefix: string, level: number }) => {
    let object = props.object;
    let level = props.level;
    let uuid = object.uuid || generateUUID();

    let div_id = "TreeItem" + uuid;
    if (object.userData.isDomExpand === undefined) {
        object.userData.isDomExpand = (level < 3) ? true : false;
    }
    let isDomExpand = object.userData.isDomExpand || false;
    let meshesCount = object.userData.meshesCount || 0;
    return (
        <div id={div_id} className="tree-item" key={uuid}>
            <div className="content" onClick={() => {
                if (Model3dViewer?.instance?.updateSelectionTarget) {
                    Model3dViewer.instance.updateSelectionTarget(object);

                    let box3 = new Box3();
                    box3.setFromObject(object);
                    console.log(object.userData,(object));
                }
                let isDomExpand = object.userData.isDomExpand || false;
                isDomExpand = !isDomExpand;
                object.userData.isDomExpand = isDomExpand;

                let c_div = document.getElementById(div_id);

                if (c_div) {
                    let children_div = c_div.getElementsByClassName("tree-children")[0];
                    if (children_div) {
                        children_div.className = 'tree-children' + (isDomExpand ? " isDomExpand" : "");
                    }
                }


            }}>{props.prefix + "-" + `(${meshesCount})` + object.name + ' ' + ((object as Mesh).isMesh ? ((object as Mesh).geometry?.attributes?.position?.count || "") : "")}</div>
            <div className={'tree-children' + (isDomExpand ? " isDomExpand" : "")}>
                {object.children && object.children.length > 0 && object.children.map((child, index) => {
                    return <Object3dTreeItem object={child} key={child.uuid + "-" + index} prefix={props.prefix + "  "} level={props.level + 1}></Object3dTreeItem>
                })}
            </div>

        </div>
    )

}
export default Object3dTreeItem;

