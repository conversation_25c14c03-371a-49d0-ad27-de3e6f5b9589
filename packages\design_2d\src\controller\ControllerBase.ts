import { Vector3 } from "three";
import { Design2DContext } from "../Design2DContext";
import { IControllerBase } from "../types/IControllerBase";
import { CoordinateTransformer } from "../utils/CoordinateTransformer";

/**
 * 基础控制器抽象类
 * 提供控制器的基础实现，其他控制器可以继承此类
 */
export abstract class ControllerBase implements IControllerBase {
    protected _type: string = '';
    protected _isActive: boolean = false;
    protected _context: Design2DContext;
    
    constructor(type: string, context: Design2DContext) {
        this._type = type;
        this._context = context;
    }

    public get type(): string {
        return this._type;
    }

    public get isActive(): boolean {
        return this._isActive;
    }

    public get context(): Design2DContext {
        return this._context;
    }
    public activate(data?: any): void {
        if (this._isActive) {
            return;
        }
        this._isActive = true;
    }

    public deactivate(): void {
        if (!this._isActive) {
            return;
        }
        this._isActive = false;
    }

    /**
     * 将屏幕坐标转换为画布坐标
     * @param screenX 屏幕X坐标
     * @param screenY 屏幕Y坐标
     * @returns 画布坐标
     */
    public screenToCanvas(point: Vector3): Vector3 {
       return CoordinateTransformer.screenToCanvas(point, this.context);
    }

    /**
     * 将画布坐标转换为世界坐标
     * @param point 画布坐标
     * @returns 世界坐标
     */
    public canvasToGlobal(point: Vector3): Vector3 {
        return CoordinateTransformer.canvasToGlobal(point, this.context);
    }

    public onMouseDown(event: MouseEvent): void {
        
    }

    public onMouseMove(event: MouseEvent): void {
    }

    public onMouseUp(event: MouseEvent): void {
    }

    public onWheel(event: WheelEvent): void {
    }

    public update(): void {
    }

    public render(): void {
    }

    public dispose(): void {
        this.deactivate();
    }
} 