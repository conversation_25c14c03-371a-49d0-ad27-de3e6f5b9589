import { I_SpacePose, I_XmlSpace, fillSpacePose } from "../IWardrobeEntityInterface";
import { NumberVariable } from "../NumberVariable";
import { XmlEntityBase } from "../XmlEntityBase";

export class XmlSpace extends XmlEntityBase implements I_XmlSpace {
    oldSpaceUidS?: string;
    splitDirN?: number;
    dirN ?: number;
    splitRatioS?: string;
    expandsU?: string;
    splitUnitS?: string;
    splitValueS?: string;
    splitUListS?: string;
    splitVListS?: string;
    splitUExpressS?: string;
    splitVExpressS?: string;
    templateTypeN?:number;
    CWhSpaceSplitComponent?: {
        SubNodeCWhSpaceSplit?: {
            indexN?: number;
            boardUisS?: string;
        };
    };
    /**
     *  当splitDirN 为 4 的时候,
     *    --- 分裂出的子空间考虑arroundIndexS
     *    --- arroundIndexS
     *      --- 2: 左侧---W -= thickness, PX += thickness
     *      --- 1: 右侧---W -= thickness
     *      --- 5: 顶部---H -= thickness
     *      --- 4: 深度横截 产生两个子空间:
     *        ---  深度depth_offset: thickness +  缝隙
     *        ---  子空间一:  D -= depth_offset, PY - depth_offset
     *        ---  子空间二:  D -= thickness + 1
     *      --- 6: 底部---H-thickness, PZ + thickness
     * 
     */
    CWhSpaceArroundComponent?: {
        arroundIndexS?: string;
    };

    arroundBoardSpaces : {[key:string]:{spacePose:I_SpacePose[] }}

    /**
     *  关联的板件实体列表
     */
    protected _attachedBoardEntities: XmlEntityBase[];

    CWhTagComponent?: {
        CWhTagFrameSpace?: Record<string, unknown>;
        CWhTagBackBoard?: {
            bbEnumS?: string;
            backDataUidS?: string;
        };
    };
    childSpaces?: XmlSpace[];


    constructor(data?: Partial<I_XmlSpace>) {
        super(data);
        this.CWhSpaceArroundComponent = data.CWhSpaceArroundComponent || {};
        this.CWhTagComponent = data.CWhTagComponent || null;
        this.CWhSpaceSplitComponent = data.CWhSpaceSplitComponent || {};
        this.oldSpaceUidS = data.oldSpaceUidS || "";
        this.splitDirN = data.splitDirN || 0;
        this.dirN = data.dirN || 0;
        this.splitRatioS = data.splitRatioS || "";
        this.expandsU = data.expandsU || "";
        this.splitVExpressS = data.splitVExpressS || "";
        this.splitUExpressS = data.splitUExpressS || "";
        this.splitUListS = data.splitUListS || "";
        this.splitVListS = data.splitVListS || "";
        this.splitValueS = data.splitValueS || "";
        this.splitUnitS = data.splitUnitS || "";
        this.templateTypeN = data.templateTypeN || 0;
        if (data) {
            if (data.childSpaces) {
                this.childSpaces = data.childSpaces.map(s => new XmlSpace(s));
                this.childSpaces.forEach((space)=>space.parent = this);
            }
        }
        this.updateValueObject();
    }


    get splitValues()
    {
        let splitVList = this.splitVListS || "";
        return splitVList.split(":").map((val)=>Number(val))
    }

    get splitUnit()
    {
        let splitUnit = this.splitUnitS || ""
        return splitUnit.split(":").map((val)=>Number(val))
    }

    get arroundIndices() : number[]
    {
        if(this?.CWhSpaceArroundComponent?.arroundIndexS)
        {
            let arroundIndexS = this.CWhSpaceArroundComponent.arroundIndexS;
            return arroundIndexS.split("|").map((val)=>Number(val));
        }
        return [];
    }

    get expands()
    {
        let expandsU = this.expandsU;
        return expandsU.split(",").map((val)=>Number(val));
    }
    public get attachedBoardEntities(): XmlEntityBase[] {
        if(!this._attachedBoardEntities)
        {
            this._attachedBoardEntities = [];
        }
        return this._attachedBoardEntities;
    }

    updateArroundBoardSpaces()
    {
        this.arroundBoardSpaces = {};
        let entityPose = fillSpacePose(this.valuesObj as I_SpacePose);
        let currentPose : I_SpacePose= {...entityPose,PX:0,PY:0,PZ:0,RX:0,RY:0,RZ:0};
        let otherPose : I_SpacePose =  null;
        let expands = this.expands;
        const thickness = 18;
        let t_py = 0;
        let t_backboard_thickness = 9;
        if(this.dirN == 1 || this.dirN == 3)
        {
            let W = currentPose.W;
            let D = currentPose.D;
            currentPose.W = D;
            currentPose.D = W;
        }


        if(this.attachedBoardEntities.length > 0)
        {
            let backBoard = this.attachedBoardEntities.find((entity)=>{
                return entity.CWhSpacePartComponent.splitTypeN == 4;
            });
            if(backBoard)
            {
                let spacePlaceRule = backBoard.CWhSpacePartComponent.spacePlaceRuleS.split(",");
                t_py = parseFloat(spacePlaceRule[9] || "0");
                t_backboard_thickness = backBoard.valuesObj.H as number;
            }
        }
        let childSpaces = this.childSpaces || [];


        let depthOffset = t_py + t_backboard_thickness;

        if(expands[0] !== 0)
        {
            currentPose.D += expands[0];
        }
        let srcPose = {...currentPose};

        const addBoardSpace = (arroundId:number, pose:I_SpacePose)=>{
            if(!this.arroundBoardSpaces[arroundId]){
                this.arroundBoardSpaces[arroundId] = {spacePose : []};
            }
            this.arroundBoardSpaces[arroundId].spacePose.push(pose);
        }   
        if(this.arroundIndices && this.arroundIndices.length > 0)
        {
            this.arroundIndices.forEach((ArroundId)=>{
                let boardPose = {...currentPose};
                if(ArroundId == 2) // 左侧板
                {
                    boardPose.W = thickness;
                    currentPose.W -= thickness;
                    currentPose.PX += thickness;
                }
                else if(ArroundId == 1)
                {
                    boardPose.W = thickness;
                    boardPose.PX = srcPose.W - thickness;
                    currentPose.W -= thickness;
                }
                else if(ArroundId == 5)
                {
                    boardPose.H = thickness;
                    boardPose.PZ = srcPose.H - boardPose.H;
                    currentPose.H -= thickness;
                }
                else if(ArroundId == 6)
                {
                    boardPose.H = thickness;
                    boardPose.PZ = 0;
                    currentPose.H -= thickness;
                    currentPose.PZ += thickness;
                }
                else if(ArroundId == 4)
                {
                    otherPose = {...currentPose};
                    currentPose.D -= depthOffset;
                    currentPose.PY -= depthOffset;

                    otherPose.D = t_py;
                }
                if(ArroundId !== 0)
                {
                    addBoardSpace(ArroundId,boardPose);                
                }
            });

            addBoardSpace(0,currentPose);
            if(otherPose)
            {
                addBoardSpace(0,otherPose);
            }

        }
        else {
            addBoardSpace(0,currentPose);
        }
        return this.arroundBoardSpaces;


        
    }

}
