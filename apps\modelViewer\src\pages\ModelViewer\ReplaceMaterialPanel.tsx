import { I_DesignMaterialInfo } from "@layoutai/basic_data";
import { DesignMaterialService, GltfManager, Model3dViewer, SvgGltfBoardBatchNode, SvgGltfFurnitureNode, SvgGltfNode, SvgGltfSwingDoorLeafNode, TextureManager } from "@layoutai/model3d_api";
import { getImgDomain } from "@svg/request";
import { Button, Segmented } from "antd";
import { useEffect, useRef, useState } from "react";
import { Group } from "three";
import "./index.less"

enum TabNames  {
    LeafDoor = "LeafDoor",
    Material = "Material",
    RealFurniture = "RealFurniture"
}

interface I_DesignMaterialItem extends I_DesignMaterialInfo
{
    MaterialId : string;
    ImagePath : string;
    tabName ?: string;
    [key:string]:any;
}

const ReplaceMaterialPanel : React.FC<{node:SvgGltfNode,onUpdated?:()=>void}>= (props:{node:SvgGltfNode,onUpdated?:()=>void})=>{
    const [tabName,setTabName] = useState<string>(TabNames.LeafDoor);

    const [materialItems,setMaterialItems] = useState<I_DesignMaterialItem[]>([]);

    const [currentItem,setCurrentItem] = useState<I_DesignMaterialItem>(null);

    const queryInputRef = useRef(null);
    const labelOptions = [
        {
            value : TabNames.LeafDoor,
            label : "门板"
        },
        {
            value : TabNames.Material,
            label : "花色"
        },
        {
            value : TabNames.RealFurniture,
            label : "饰品"
        }
    ]

    const queryMaterialId = async (materialId:string)=>{
        let item = materialItems.find((item)=>item.MaterialId === materialId);
        if(!item)
        {
            let res = await DesignMaterialService.getDesignMaterialInfoByIds([materialId]);
            if(res && res[0])
            {
                item = res[0];
                item.tabName = tabName;
                if(res[0].A3dSource && res[0].A3dSource.length > 0)
                {
                    if(tabName !== TabNames.RealFurniture)
                    {
                        confirm("对象不是成品");
                        return;
                    }

                }
                else if(res[0].ContentUrl && res[0].ContentUrl.length > 0)
                {
                    if(tabName !== TabNames.LeafDoor)
                    {
                        confirm("不是参数化模型");
                        return;
                    }
     
                }
                else {
                    if(tabName !== TabNames.Material)
                    {
                        setTabName(TabNames.Material);
                        item.tabName = TabNames.Material;
                    }
                }
                setMaterialItems([item,...materialItems]);
                saveHistory([item,...materialItems]);

            }
        }

        if(item)
        {
            setCurrentItem(item);
            applyItem(item);
        }

    }

    const saveHistory = (materialItems:I_DesignMaterialItem[])=>{
        if(localStorage)
        {
            materialItems.length = Math.min(materialItems.length,50);
            localStorage.setItem("LayoutAI_ReplaceMaterial",JSON.stringify(materialItems));
        }
    }

    const loadHistory = ()=>{
        if(localStorage)
        {
            let item = localStorage.getItem("LayoutAI_ReplaceMaterial");
            if(item)
            {
                try {
                    let data = JSON.parse(item);
                    setMaterialItems(data);

                } catch (error) {
                    
                }
            }
        }
    }

    
    const applyItem = async (item : I_DesignMaterialItem)=>{
        let selectedTarget = Model3dViewer.instance.seletionTarget;
        if(!selectedTarget) return;

        if(selectedTarget instanceof SvgGltfFurnitureNode)
        {
            if(tabName === TabNames.RealFurniture)
            {
                await GltfManager.replaceRealFurnitureMaterialId(selectedTarget,item.MaterialId,{designMaterialInfo:item});
            }

        }
        else if(selectedTarget instanceof SvgGltfSwingDoorLeafNode)
        {
            if(item)
            {
                if(tabName === TabNames.Material)
                {
                   await  GltfManager.replaceMaterialMapVoId(selectedTarget,item.MaterialId,{designMaterialInfo:item});
                }
                else if(tabName === TabNames.LeafDoor)
                {
                    if(selectedTarget instanceof SvgGltfSwingDoorLeafNode)
                    {
                        let materialMapVoId = selectedTarget.materialMapVoId;
                        await GltfManager.replaceSwingDoorLeafMaterialId(selectedTarget,item.MaterialId,{designMaterialInfo:item});
                        if(materialMapVoId && materialMapVoId.length > 0)
                        {
                            let textureItem = TextureManager.instance.findTextureInCache(materialMapVoId);
                            await GltfManager.replaceMaterialMapVoId(selectedTarget as SvgGltfSwingDoorLeafNode,materialMapVoId,{imgPath:textureItem.imgUrl});
                        }

                    }
                }

                if(props.onUpdated)
                {
                    props.onUpdated();
                }
            }
        }
        else if(selectedTarget instanceof SvgGltfBoardBatchNode)
        {
            if(item)
            {
                if(tabName === TabNames.Material)
                {
                   await  GltfManager.replaceMaterialMapVoId(selectedTarget,item.MaterialId,{designMaterialInfo:item});
                }
            }
            if(props.onUpdated)
            {
                props.onUpdated();
            }
        }
    }
    useEffect(()=>{
        loadHistory();
    },[])
    useEffect(()=>{



    },[currentItem]);
    return <div className="SeriesContainer">
        <Segmented options={labelOptions}  value={tabName} onChange={(name)=>{
            setTabName(name);
        }}></Segmented>
        <div>
            <input className="InputMaterial" ref={queryInputRef}></input><Button onClick={()=>{
                if(queryInputRef.current)
                {
                    let materialId = queryInputRef.current.value;
                    queryMaterialId(materialId);

                }
            }}>请求</Button>
        </div>
        {materialItems.filter((item)=>item.tabName==tabName).map((item,index)=><div className={"style_item"+" "+(item===currentItem?"selectedItem":"")} key={"item"+index} onClick={(ev)=>{
            setCurrentItem(item);
            applyItem(item);
        }}>
                <img src={getImgDomain()+item.ImagePath}></img>
                <label>{item.MaterialName}:{item.MaterialId}</label>

        </div>)}
    </div>



}

export default ReplaceMaterialPanel;