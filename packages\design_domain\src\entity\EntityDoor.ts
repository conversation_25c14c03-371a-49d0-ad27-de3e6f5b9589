import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { Vector3 } from "three";
import { ZRect } from "@layoutai/z_polygon";

import { I_DesignMaterialInfo, IType2UITypeDict } from "@layoutai/basic_data";
import { MaterialService, TMaterialMatchingConfigs } from "@layoutai/series_matcher";

import { TSerialSizeRangeDB } from "../service/series/TSeriesSizeRangeDB";
import { TSize } from "../service/series/TSizeRange";
import { EntityBase } from "./EntityBase";
import { IEntityRoom } from "./EntityRoom";
import { EntityType } from "./EntityType";
import { IEntityWall } from "./EntityWall";
import { WinDoorEntityCategory } from "./EntityType"

/**
 * @description 门实体
 */
export const EntityDoor = types
    .compose(
        EntityBase,
        types.model({
            // 是否可见，默认为 true 可见
            visible: types.optional(types.boolean, true),
            // 类型
            eType: types.string,
            realType: types.string,
            // 镜像
            mirror: types.number,
            // 位置
            x: types.number,
            y: types.number,
            z: types.optional(types.number, 0),
            // 法线
            normalX: types.number,
            normalY: types.number,
            normalZ: types.number,
            // 尺寸
            length: types.number,
            width: types.number,
            height: types.optional(types.number, 2200),
            // 房间
            roomTypes: types.optional(types.array(types.string), []),
        })
    )
    .named(EntityType.door)
    .props({
        type: EntityType.door,
    })
    .volatile(self => ({
        walls: [] as IEntityWall[],
        room: null as IEntityRoom | null,
    }))
    .actions((self) => ({
        // 设置位置
        setX(x: number) {
            self.x = x;
        },
        setY(y: number) {
            self.y = y;
        },
        setZ(z: number) {
            self.z = z;
        },
        setPosition(x: number, y: number, z: number) {
            self.x = x;
            self.y = y;
            self.z = z;
        },
        // 设置法线
        setNormal(normalX: number, normalY: number, normalZ: number) {
            self.normalX = normalX;
            self.normalY = normalY;
            self.normalZ = normalZ;
        },
        // 设置尺寸
        setLength(length: number) {
            self.length = length;
        },
        setWidth(width: number) {
            self.width = width;
        },
        setHeight(height: number) {
            self.height = height;
        },
        setSize(length: number, width: number, height: number) {
            self.length = length;
            self.width = width;
            self.height = height;
        },
        // 设置是否可见
        setVisible(visible: boolean) {
            self.visible = visible;
        },
        addWall(wall: IEntityWall) {
            self.walls.push(wall);
        },
        clearWallList() {
            self.walls = [];
        },
        setRoom(room: IEntityRoom) {
            self.room = room;
        },
        clearRoom() {
            self.room = null;
        },
    }))
    .views((self) => ({
        get thickness() {
            return self.width;
        },
        get category() {
            let room_types = self.roomTypes.slice();
            if (room_types.length == 0) {
                return undefined;
            }
            room_types.sort((a, b) => TMaterialMatchingConfigs.roomTypesOrders.indexOf(b) - TMaterialMatchingConfigs.roomTypesOrders.indexOf(a));
            return TMaterialMatchingConfigs.roomType2DoorModellocMap.get(room_types[0]) || WinDoorEntityCategory.OtherDoor;
        },
        get subCategory() {
            return IType2UITypeDict[self.realType || self.eType];
        },
        get rect() {
            const r = new ZRect(self.length, self.width);
            r.nor = new Vector3(self.normalX, self.normalY, self.normalZ);
            r.rect_center_3d = new Vector3(self.x, self.y, self.z);
            if (self.mirror === 1) {
                r.invertOrder();
            }
            r.updateRect();
            return r;
        },
        get materialId(): string | undefined {
            let size = new TSize(this.rect.w - 0.1, this.rect.h - 0.1, 0);
            let res = TSerialSizeRangeDB.QueryDefaultModelIds(this.subCategory, size);
            if (!res?.length) {
                console.error("default materialId is null", this.subCategory);
                return;
            }
            return res[0];
        },
        async materialInfo(): Promise<I_DesignMaterialInfo | undefined> {
            if (!this.materialId) {
                return;
            }
            let dvoList = await MaterialService.getDesignMaterialInfoByIds([this.materialId]);
            if (!dvoList.length) {
                return;
            }
            return dvoList[0];
        }
    }));

export interface IEntityDoor extends Instance<typeof EntityDoor> { }
export interface IEntityDoorSnapshotIn extends SnapshotIn<typeof EntityDoor> { }
export interface IEntityDoorSnapshotOut extends SnapshotOut<typeof EntityDoor> { }