import type { IState } from "../../interfaces/IState";
import { Design2DStates } from "../../const/FSMConst";
import { appContext } from "../../../AppContext";
import { DesignControllerType } from "../../../ctrls/DesignControllerType";

export class DrawWinDoorState implements IState {
    name: string;
    parent?: any;

    constructor(name: string = Design2DStates.DRAW_WINDOOR) {
        this.name = name;
    }

    onEnter(data?: any): void {
        const design2DContext = appContext.design2DContext;
        if (design2DContext) {
            design2DContext.controllerManager.activateCtrl(DesignControllerType.DRAW_WINDOOR_CTRL, data);
            design2DContext.controllerManager.deactivateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
        }
        // 状态进入逻辑
    }

    onExit(data?: any): void {
        // 状态退出逻辑
        const design2DContext = appContext.design2DContext;
        if (design2DContext) {
            design2DContext.controllerManager.activateCtrl(DesignControllerType.CANVAS_LMB_MOVE);
            design2DContext.controllerManager.deactivateCtrl(DesignControllerType.DRAW_WINDOOR_CTRL);
        }
    }

    /**
     * 切换到3D模式
     */
    switchTo3D(): void {}
}
