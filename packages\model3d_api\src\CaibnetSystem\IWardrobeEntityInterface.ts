import { <PERSON><PERSON><PERSON>, Vector3 } from "three";

export interface I_XmlEntityBase{
    EntityName ?: string;
    uidN?: number;
    uidS?: string;
    nameS?: string;
    nameExpS?: string;
    visibleB?: boolean;
    visibleExpS ?: string;
    DATAFORM?: string;
    materialIdS?: string;
    typeS?: string;
    partNumberS?: string;
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
        StringVariable?: IStringVariable[];
    };
    CWhSpacePartComponent?: I_XmlCWhSpacePartComponent;
    Children?: {
        CWhSpaceEntity?: I_XmlCWhSpaceEntity[];
        CRealFurnitureEntity ?:I_XmlCRealFurnitureEntity[];
        CBoardPartEntity?: I_XmlCBoardPartEntity[];
        CSwingdoorEntity?: I_XmlCSwingdoorEntity[];
        CSwingdoorLeafEntity?: I_XmlCSwingdoorLeafEntity[];
        CDoorBoardEntityBase?: I_XmlCDoorBoardEntityBase[];
        CHandlePartEntity?: I_XmlCHandlePartEntity[];
        [key:string]:I_XmlEntityBase[];
    };
}
export interface I_XmlStdWardrobeEntity  extends I_XmlEntityBase {
    nameS?: string;
    visibleB?: boolean;
    DATAFORM?: string;
    nameExpS?: string;
    materialIdS?: string;
    typeS?: string;
    partNumberS?: string;
    isFunctorSyncB?: boolean;
    isQuoteB?: boolean;
    classTypeS?: string;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    spaceSaveTypeN?: number;
    unitEnumN?: number;
    styleSchemeVoIdS?: string;
    RoomIdS?: string;
    EntityName ?: string;
    __protoflag__?: string;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: Record<string, unknown>;
    CBackBoardComponent?: {
        CGenerateBackBoardData?: {
            idS?: string;
            bbEnumN?: number;
            configIdS?: string;
            generateIdsU?: string;
            braceIdsU?: string;
            backBoardIdsU?: string;
            childrenIdsU?: string;
            boardStyleIdS?: string;
            braceVoS?: string;
            craftVoS?: string;
            hasSlotB?: boolean;
            createTypeN?: number;
            spaceIdN?: number;
            mainPartIdN?: number;
        };
    };

    Children?: {
        CWhSpaceEntity?: I_XmlCWhSpaceEntity[];
        CRealFurnitureEntity ?:I_XmlCRealFurnitureEntity[];
        CBoardPartEntity?: I_XmlCBoardPartEntity[];
        CSwingdoorEntity?: I_XmlCSwingdoorEntity[];
        [key:string]:I_XmlEntityBase[];
    };
    MaterialIDs?: {
        idsS?: string;
    };
    CabinetCornerCuts?: Record<string, unknown>;
}

export interface I_XmlCWardrobeGroupEntity extends I_XmlStdWardrobeEntity {
    newRelatedIdsS?: string;
    oldRelatedIDS?: string;
    RoomIdS?: string;
    __protoflag__?: string;
}
export interface I_XmlCWardrobeEntity extends I_XmlStdWardrobeEntity {

}

export interface INumberVariable {
    nameS?: string;
    valueN?: number;
    valueExpressionS?: string;
    isUserChangedB?: boolean;
    isShowSizeB?: boolean;
    aliasS?: string;
    minValueN?: number;
    maxValueN?: number;
    minValueExpressS?: string;
    maxValueExpressS?: string;
    isShowInLayoutB?: boolean;
    visibleExpressionS?: string;
    remarkS?: string;
    isGlobalB?: boolean;
    ExpOption?: IExpOption[];
}



export interface IStringVariable {
    nameS?: string;
    valueS?: string;
    valueExpressionS?: string;
    defaultProduceB?: boolean;
}

export interface IExpOption {
    valueS?: string;
    aliasS?: string;
}

export interface I_XmlCWhSpaceEntity  extends I_XmlSpace  {
    nameS?: string;
    visibleB?: boolean;
    oldSpaceUidS?: string;
    splitDirN?: number;
    splitRatioS?: string;
    expandsU?: string;
    layoutDvoIdS?: string;
    layoutNameS?: string;
    layoutVerS?: string;
    CWhSpaceArroundComponent?: {
        arroundIndexS?: string;
    };
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
    childSpaces?: I_XmlSpace[];
}
export interface I_XmlCRealFurnitureEntity   extends I_XmlEntityBase {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    materialIdS?: string;
    xN?: number;
    yN?: number;
    zN?: number;
    widthN?: number;
    heightN?: number;
    lengthN?: number;
    typeS?: string;
    materialAttributeS?: string;
    mirrorTypeS?: string;
    mirrorPointS?: string;
    mirrorNormalS?: string;
    generationTypeS?: string;
    RoomIdS?: string;
    enabledB?: boolean;
    affectDiffuseB?: boolean;
    affectSpecularB?: boolean;
    invisibleB?: boolean;
    colorN?: number;
    intensityN?: number;
    unitsN?: number;
    lightGroupS?: string;
    isAutoIntensityB?: boolean;
    autoIntensityN?: number;
}

/**
 *  空间位姿九值
 */
export interface I_SpacePose
{
    W : number;
    D : number;
    H : number;
    PX : number;
    PY : number;
    PZ : number;
    RX : number;
    RY : number;
    RZ : number;
    [key:string]: number;
}

export interface I_BoardPose extends I_SpacePose
{
    BLM : number;
    BRM : number;
    BUM : number;
    BDM : number;
    BFM : number;
    BBM : number;
    [key:string]: number;
}
const ISpacePoseKeys = ["W","D","H","PX","PY","PZ","RX","RY","RZ"];
const IBoardPoseKeys = ["W","D","H","PX","PY","PZ","RX","RY","RZ","BLM","BRM","BUM","BFM","BBM"];
export function cloneSpacePose(data:any)
{
    let pose : I_SpacePose = {W:0,D:0,H:0,PX:0,PY:0,PZ:0,RX:0,RY:0,RZ:0};
    let keys = ISpacePoseKeys;
    keys.forEach((key)=>{
        if(data[key] && (typeof data[key] === "number"))
        {
            pose[key] = data[key];
        }
    });
    return pose;
}
export function fillSpacePose(data:I_SpacePose,defaultValue:I_SpacePose={W:0,D:0,H:0,PX:0,PY:0,PZ:0,RX:0,RY:0,RZ:0})
{
    let keys = ISpacePoseKeys;
    keys.forEach((key)=>{
        if(!data[key])
        {
            data[key] = defaultValue[key];
        }
    });

    return data;
}
export function fillBoardPose(data:I_BoardPose,defaultValue:I_BoardPose={W:1,D:1,H:1,PX:0,PY:0,PZ:0,RX:0,RY:0,RZ:0,BLM:0,BRM:0,BBM:0,BDM:0,BFM:0,BUM:0})
{
    let keys = IBoardPoseKeys;
    keys.forEach((key)=>{
        if(!data[key])
        {
            data[key] = defaultValue[key];
        }
    });

    return data;
}
export function getObjectFromNumberVariables(numberVariables:INumberVariable[])
{
    let res:{[key:string]:number} = {};
    numberVariables.forEach((num)=>res[num.nameS||""]= (num.valueN||0));
    return res;
}
export function getBoardPoseDataBySpacePose(data:I_SpacePose)
{
    let size0 = new Vector3(data.W,-data.D,data.H);
    let pos0 = new Vector3(data.PX,data.PY,data.PZ);
    let rotate = new Euler(data.RX/180*Math.PI,data.RY/180*Math.PI,data.RZ/180*Math.PI,"ZYX");
    size0.applyEuler(rotate);
    size0.y = -size0.y;

    let boardPose : I_BoardPose = fillBoardPose({...data} as I_BoardPose);
    boardPose.BBM = Math.min(-pos0.y, -pos0.y + size0.y);
    boardPose.BFM = Math.max(-pos0.y, -pos0.y + size0.y);
    boardPose.BDM = Math.min(pos0.z,pos0.z + size0.z);
    boardPose.BUM = Math.max(pos0.z,pos0.z + size0.z);
    boardPose.BLM = Math.min(pos0.x, pos0.x + size0.x);
    boardPose.BRM = Math.max(pos0.x, pos0.x + size0.x);

    for(let key in IBoardPoseKeys)
    {
        if(boardPose[key]){
            boardPose[key] = Math.round(boardPose[key] * 100.) / 100;
        }
    }
    return boardPose;

}
export function isEqualsSpacePose(pose0:I_SpacePose, pose1:I_SpacePose,notEqualKeys?:string[])
{
    let ans = true;
    ISpacePoseKeys.forEach((key)=>{
        let val0 = pose0[key] || 0;
        let val1 = pose1[key] || 0;
        if(Math.abs(val0 - val1) > 0.01)
        {
            if(notEqualKeys) notEqualKeys.push(key);
            ans = false;
        }
    })
    return ans;
}

export function isEqualsBoardPose(pose0:I_BoardPose, pose1:I_BoardPose,notEqualKeys?:string[])
{
    let ans = true;
    IBoardPoseKeys.forEach((key)=>{
        let val0 = pose0[key] || 0;
        let val1 = pose1[key] || 0;
        if(Math.abs(val0 - val1) > 0.01)
        {
            if(notEqualKeys) notEqualKeys.push(key);
            ans = false;
        }
    })
    return ans;
}

export interface I_XmlSpace   extends I_XmlEntityBase {
    nameS?: string;
    uidN?: number;
    dirN ?: number;
    visibleB?: boolean;
    oldSpaceUidS?: string;
    splitDirN?: number;
    splitRatioS?: string;
    expandsU?: string;
    splitUnitS?: string;
    splitValueS?: string;
    splitUListS?: string;
    splitVListS?: string;
    splitUExpressS?: string;
    splitVExpressS?: string;
    templateTypeN?:number;
    CWhSpaceSplitComponent?: {
        SubNodeCWhSpaceSplit?: {
            indexN?: number;
            boardUisS?: string;
        };
    };
    CWhSpaceArroundComponent?: {
        arroundIndexS?: string;
    };
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
    CWhTagComponent?: {
        CWhTagFrameSpace?: Record<string, unknown>;
        CWhTagBackBoard?: {
            bbEnumS?: string;
            backDataUidS?: string;
        };
    };
    childSpaces?: I_XmlSpace[];
}
export interface I_XmlCWhSpacePartComponent{
    spaceUidN?: number;
    roundSpaceExUidN?: number;
    spacePlaceRuleS?: string;
    spacePlacedefaultTypeS?: string;
    splitTypeN?: number;
    SpaceVariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
};
export interface I_XmlCBoardPartEntity   extends I_XmlEntityBase {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    DATAFORM?: string;
    nameExpS?: string;
    materialIdS?: string;
    typeS?: string;
    subTypeS?: string;
    partNumberS?: string;
    isMovableB?: boolean;
    isFunctorSyncB?: boolean;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: {
        isReversePlaneN?: number;
        isRotateN?: number;
        materialMapVoIdS?: string;
    };
    CWhSpacePartComponent?: I_XmlCWhSpacePartComponent;
    EdgeComponent?: {
        fatEdgeStartN?: number;
        fatEdgeEndN?: number;
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeBandArrS?: string;
        edgeModifyByUserN?: number;
    };
    CWhTagComponent?: {
        CWhTagFrameSpace?: Record<string, unknown>;
        CWhTagBackBoard?: {
            bbEnumS?: string;
            backDataUidS?: string;
        };
    };
}
export interface I_XmlCFuntorEntity extends I_XmlEntityBase {
    
}
export interface I_XmlCSwingdoorEntity   extends I_XmlEntityBase {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    DATAFORM?: string;
    nameExpS?: string;
    typeS?: string;
    isMovableB?: boolean;
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    doorTypeN?: number;
    isChangedFromSpaceBoardB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: Record<string, unknown>;
    CWhSpacePartComponent?: {
        spaceUidN?: number;
        spacePlaceRuleS?: string;
        SpaceVariableComponet?: {
            NumberVariable?: INumberVariable[];
        };
    };
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
    Children?: {
        CSwingdoorLeafEntity?: I_XmlCSwingdoorLeafEntity[];
    };
    EdgeComponent?: {
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeModifyByUserN?: number;
    };
    GenerationEntityVo?: {
        generatedBoardsLimitNamesS?: string;
        recordFunctorLimitNameArrS?: string;
        parentIdsS?: string;
    };
}

export interface I_XmlCSwingdoorLeafEntity   extends I_XmlEntityBase {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    DATAFORM?: string;
    nameExpS?: string;
    typeS?: string;
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    leafTypeN?: number;
    openDirectionN?: number;
    openPositionN?: number;
    hingeMountN?: number;
    connectDoorIdS?: string;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: Record<string, unknown>;
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
    Children?: {
        CDoorBoardEntityBase?: I_XmlCDoorBoardEntityBase[];
        CHandlePartEntity?: I_XmlCHandlePartEntity[];
    };
    EdgeComponent?: {
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeModifyByUserN?: number;
    };
}

export interface I_XmlCDoorBoardEntityBase   extends I_XmlEntityBase  {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    DATAFORM?: string;
    nameExpS?: string;
    materialIdS?: string;
    typeS?: string;
    subTypeS?: string;
    partNumberS?: string;
    isFunctorSyncB?: boolean;
    placeRuleS?: string;
    isQuoteB?: boolean;
    isBoardPointEditUnlimitedB?: boolean;
    isQuoteSplitN?: number;
    isOutsourcingN?: number;
    ocTypeS?: string;
    standardCategoryS?: string;
    isSplitN?: number;
    isSetThI_XmlCkN?: number;
    canReplaceMaterialB?: boolean;
    canSetHandleSizeB?: boolean;
    reverseSizeB?: boolean;
    textureDirectionN?: number;
    doorBoardMaterialChangeS?: string;
    canEditDoorBoardThI_XmlCkB?: boolean;
    Visible?: {
        MiddleVariable?: {
            nameS?: string;
            valueS?: string;
            valueExpressionS?: string;
        };
    };
    CWhMaterialComponent?: {
        materialMapVoIdS?: string;
        isRotateN?: number;
        oriMaterialMapVoIdS?: string;
    };
    VariableComponet?: {
        NumberVariable?: INumberVariable[];
    };
    Children?: {
        CDoorBoardEntityBase?: I_XmlCDoorBoardEntityBase[];
        CFaceBoardEntity?: I_XmlCFaceBoardEntity[];
    };
    EdgeComponent?: {
        fatEdgeStartN?: number;
        fatEdgeEndN?: number;
        edgeBandModeN?: number;
        edgeBandPlanInfoS?: string;
        edgeBandArrS?: string;
        edgeModifyByUserN?: number;
    };
}

export interface I_XmlCFaceBoardEntity   extends I_XmlEntityBase  {
    uidN?: number;
    visibleB?: boolean;
    MaterialComponent?: {
        materialMapVoIdS?: string;
        isRotateN?: number;
        oriMaterialMapVoIdS?: string;
    };
    PointDef?: {
        posXS?: string;
        posYS?: string;
        typeS?: string;
        centerXS?: string;
        centerYS?: string;
        angleS?: string;
        sectionS?: string;
    }[];
}

export interface I_XmlCHandlePartEntity {
    nameS?: string;
    uidN?: number;
    visibleB?: boolean;
    DATAFORM?: string;
    buckleMaterialIdS?: string;
    materialIdS?: string;
    nameExpS?: string;
    otherTypeS?: string;
    rotateDegressS?: string;
    offsetXN?: number;
    offsetYN?: number;
    offsetZN?: number;
    installTypeN?: number;
    isHorizontalB?: boolean;
    doorKeyVisibleB?: boolean;
    isSpyHoleB?: boolean;
    backInstallFlagB?: boolean;
    isBackInstallB?: boolean;
    isUnlimitedOffsetB?: boolean;
    canSetHandleSizeB?: boolean;
    widthN?: number;
    depthN?: number;
    heightN?: number;
    subTypeS?: string;
    typeS?: string;
    CWhMaterialComponent?: Record<string, unknown>;
}


export function parseWardrobeXML(rootElement: Element) {    
    // Helper function to convert element attributes to proper types
    function parseAttributes(element: Element): any {
        const result: any = {};
        
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            const name = attr.name;
            let value: string | number | boolean = attr.value;
            
            // Apply type conversion based on attribute name suffix
            if (name.endsWith('N')) {
                value = parseFloat(value);
            } else if (name.endsWith('B')) {
                value = value === 'T' || value === 'true';
            }
            // else remains string
            
            result[name] = value;
        }

        return result;
    }
    
    // Helper function to parse a single element
    function parseElement(element: Element): any {
        const result = parseAttributes(element);

        if(element.tagName.endsWith("Entity") || element.tagName.endsWith("EntityBase") || element.tagName.endsWith("space"))
        {
            result.EntityName = element.tagName;
        }
        // Handle child elements
        for (let i = 0; i < element.children.length; i++) {
            const child = element.children[i];
            const childName = child.tagName;
            // Handle arrays
            if (['NumberVariable','StringVariable', 'space','PointDef'].includes(childName) || childName.endsWith("Entity") || childName.endsWith("EntityBase")) {
                let keyName = childName;
                if(childName === "space")
                {
                    keyName = "childSpaces";
                }
                if (!result[keyName]) {
                    result[keyName] = [];
                }
                result[keyName].push(parseElement(child));
            } 
            // Handle objects
            else {
                result[childName] = parseElement(child);
            }

        }
        
        return result;
    }
    
    return parseElement(rootElement) as (I_XmlCWardrobeEntity |I_XmlCWardrobeGroupEntity);
}
