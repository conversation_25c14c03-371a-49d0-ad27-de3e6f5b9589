import { ControllerBase, Design2DContext } from "@layoutai/design_2d";
import type { Object2DBase } from "@layoutai/design_2d";
import { appContext } from "../../AppContext";
import { Design2DStates } from "../../fsm/const/FSMConst";
import { DesignControllerType } from "../DesignControllerType";
import { Vector3 } from "three";
import { DesignEntityType } from "../../object/DesignEntityType";
import { DomainApiHub, type IEntityDoor, type IEntityWindow } from "@layoutai/design_domain";
import { g_FigureImagePaths } from "@layoutai/design_2d";
import type { IRoomEntityType, IRoomEntityRealType } from "@layoutai/basic_data";

/**
 * 门窗绘制控制器
 * 专门负责门窗的绘制操作
 */

const EntityType: { [key: string]: IRoomEntityType } = {
    WINDOW: "Window",
    DOOR: "Door",
};

const WinDoorMapping: {
    [key: string]: { type: IRoomEntityType; realType: IRoomEntityRealType };
} = {
    单开门: { type: EntityType.DOOR, realType: "SingleDoor" },
    推拉门: { type: EntityType.DOOR, realType: "SlidingDoor" },
    一字窗: { type: EntityType.WINDOW, realType: "OneWindow" },
    飘窗: { type: EntityType.WINDOW, realType: "BayWindow" },
    双开门: { type: EntityType.DOOR, realType: "DoubleDoor" },
    子母门: { type: EntityType.DOOR, realType: "SafetyDoor" },
    门洞: { type: EntityType.DOOR, realType: "DoorHole" },
    垭口: { type: EntityType.DOOR, realType: "PassDoor" },
    栏杆: { type: EntityType.WINDOW, realType: "Railing" },
};

export class DrawWinDoorController extends ControllerBase {
    private _mouseDownPoint: { x: number; y: number } | null = null;
    private _selectItem: any | null = null;
    private _object2D: Object2DBase | null = null;

    private _rafId: number | null = null;

    constructor(context: Design2DContext) {
        super(DesignControllerType.DRAW_WINDOOR_CTRL, context);
    }

    public initialize(): void {
        this._reset();
    }

    public activate(data: any): void {
        super.activate();
        this._reset();
        // 初次点击需要触发绘制
        if (data) {
            const { event, item } = data as { event: MouseEvent; item: any };
            this._selectItem = item;
            this.onMouseDown(event);
        }
    }

    public deactivate(): void {
        super.deactivate();
        this._reset();
    }

    // 获取世界坐标
    private _getGlobalPoint(event: MouseEvent): { x: number; y: number } {
        const canvasPoint = this.screenToCanvas(new Vector3(event.clientX, event.clientY));
        const globalPoint = this.canvasToGlobal(canvasPoint);
        return { ...globalPoint };
    }

    // 创建门窗实体
    private async _addWinDoorEntity(): Promise<string | null> {
        const title = this._selectItem.title;
        const config = { ...g_FigureImagePaths[title], ...WinDoorMapping[title] };
        const pose = {
            x: this._mouseDownPoint?.x || 0,
            y: this._mouseDownPoint?.y || 0,
            // 默认法向
            normalX: 0,
            normalY: -1,
            normalZ: 0,
            length: config.length!,
            width: config.depth!,
        };
        let uuid: string = "";
        if (config && config.type == EntityType.WINDOW) {
            uuid = await DomainApiHub.instance.createWindow(config.type, config.realType, pose);
        } else if (config && config.type == EntityType.DOOR) {
            uuid = await DomainApiHub.instance.createDoor(config.type, config.realType, pose);
        }
        return uuid;
    }

    public async onMouseDown(event: MouseEvent): Promise<void> {
        if (event.button == 1) return;
        const title = this._selectItem.title;
        // 坐标转换
        this._mouseDownPoint = this._getGlobalPoint(event);
        // 创建实体
        const uuid = await this._addWinDoorEntity();
        if (uuid) {
            // 创建2D对象
            const entityType = WinDoorMapping[title].type === EntityType.WINDOW ? DesignEntityType.window : DesignEntityType.door;
            const object2D = appContext.design2DContext?.object2DManager.createObject2D(entityType, uuid);
            if (object2D) {
                this._object2D = object2D;
                console.log(this._object2D);
                this.context.canvas2DManager.updateCanvas(true);
            }
        }
    }

    public onMouseMove(event: MouseEvent): void {
        if (!this._mouseDownPoint || !this._object2D) return;

        // 渲染优化
        if (this._rafId !== null) return;
        this._rafId = requestAnimationFrame(() => {
            this._rafId = null;

            // 更新坐标
            this._mouseDownPoint = this._getGlobalPoint(event);
            // 更新实体位置
            if (this._object2D && this._object2D.entity) {
                const entity = this._object2D.entity as IEntityWindow | IEntityDoor;
                entity.setX(this._mouseDownPoint.x);
                entity.setY(this._mouseDownPoint.y);
            }
            // 更新画布
            appContext.design2DContext?.canvas2DManager.updateCanvas(true);
        });
    }

    public onMouseUp(event: MouseEvent): void {
        if (event.button == 1) return;
        this._reset();
        appContext.mainFSM.transitionTo(Design2DStates.IDLE2D);
    }

    private _reset(): void {
        // 清理动画帧
        if (this._rafId !== null) {
            cancelAnimationFrame(this._rafId);
            this._rafId = null;
        }

        if (this._object2D) {
            this.context.object2DManager.removeObject2D(this._object2D.uuid);
            this._object2D = null;
            this.context.canvas2DManager.updateCanvas(true);
        }
        this._mouseDownPoint = null;
    }

    public dispose(): void {
        this._reset();
        super.dispose();
    }
}
